# Epoll 网络服务器设计方案

## 1. 目标

*   实现一个基于 Epoll 的高性能、并发 TCP 服务器。
*   能够处理大量并发连接。
*   支持非阻塞 I/O。
*   提供清晰的模块化设计，易于扩展和维护。

## 2. 核心组件

我们将设计以下核心组件：

*   **`EpollServer` 结构体：** 封装 Epoll 实例、监听器、连接管理和事件循环。
*   **`Connection` 结构体：** 表示一个客户端连接，包含文件描述符、读写缓冲区等。
*   **事件处理器接口：** 定义处理不同类型事件（如新连接、数据可读、连接关闭）的方法。

## 3. 详细设计

### 3.1 `EpollServer` 结构体

```go
type EpollServer struct {
    epollFD    int          // Epoll 实例的文件描述符
    listener   net.Listener // TCP 监听器
    connections map[int]*Connection // 管理所有活跃的连接 (fd -> *Connection)
    handler    EventHandler // 事件处理器接口
    bufferPool sync.Pool    // 缓冲区池，用于复用读写缓冲区
}
```

**字段解释：**

*   `epollFD`: Epoll 实例的文件描述符，通过 `syscall.EpollCreate1` 创建。
*   `listener`: `net.Listener` 接口，用于接受新的 TCP 连接。
*   `connections`: 一个 `map`，键是文件描述符 (int)，值是 `*Connection`，用于快速查找和管理活跃的客户端连接。
*   `handler`: 一个 `EventHandler` 接口实例，用于处理各种 Epoll 事件。这将使服务器逻辑与业务逻辑分离。
*   `bufferPool`: `sync.Pool` 用于复用 `[]byte` 缓冲区，减少内存分配和垃圾回收的开销。

### 3.2 `Connection` 结构体

```go
type Connection struct {
    fd     int        // 客户端连接的文件描述符
    remoteAddr net.Addr   // 客户端远程地址
    readBuffer []byte     // 读缓冲区
    // writeBuffer []byte // 写缓冲区 (如果需要异步写)
}
```

**字段解释：**

*   `fd`: 客户端连接的文件描述符。
*   `remoteAddr`: 客户端的远程网络地址。
*   `readBuffer`: 用于从连接读取数据的缓冲区。

### 3.3 `EventHandler` 接口

```go
type EventHandler interface {
    OnConnect(conn *Connection) error // 新连接建立时调用
    OnRead(conn *Connection, data []byte) error // 收到数据时调用
    OnClose(conn *Connection) error // 连接关闭时调用
    OnError(conn *Connection, err error) // 发生错误时调用
}
```

**方法解释：**

*   `OnConnect`: 当一个新的客户端连接被接受并添加到 Epoll 时调用。
*   `OnRead`: 当客户端连接有数据可读时调用。`data` 参数是读取到的数据。
*   `OnClose`: 当客户端连接关闭时调用。
*   `OnError`: 当处理连接时发生错误时调用。

### 3.4 `EpollServer` 方法

*   **`NewEpollServer(port string, handler EventHandler) (*EpollServer, error)`**
    *   初始化 `EpollServer` 实例。
    *   创建 `net.Listen` 监听器。
    *   创建 Epoll 实例 (`syscall.EpollCreate1`)。
    *   将监听器文件描述符添加到 Epoll 中，关注 `EPOLLIN` 事件。
    *   初始化 `connections` map 和 `bufferPool`。

*   **`Start()`**
    *   进入 Epoll 事件循环 (`syscall.EpollWait`)。
    *   根据事件类型分发到 `handleAccept` 或 `handleReadWrite`。

*   **`handleAccept(listenFD int)`**
    *   接受新连接 (`syscall.Accept`)。
    *   设置新连接为非阻塞模式 (`syscall.SetNonblock`)。
    *   创建 `Connection` 实例。
    *   将新连接的文件描述符添加到 Epoll 中，关注 `EPOLLIN | EPOLLET` 事件。
    *   调用 `handler.OnConnect`。

*   **`handleReadWrite(connFD int)`**
    *   从 `connections` map 中获取 `Connection` 实例。
    *   从连接读取数据 (`syscall.Read`)，使用 `bufferPool` 获取缓冲区。
    *   如果读取到数据，调用 `handler.OnRead`。
    *   如果读取到 0 字节，表示连接关闭，调用 `handler.OnClose` 并关闭文件描述符。
    *   如果发生错误（特别是 `EAGAIN` 或 `EWOULDBLOCK`），则跳过。其他错误调用 `handler.OnError` 并关闭连接。
    *   将缓冲区放回 `bufferPool`。

*   **`CloseConnection(fd int)`**
    *   从 Epoll 中删除文件描述符 (`syscall.EpollCtl(EPOLL_CTL_DEL)`)。
    *   关闭文件描述符 (`syscall.Close`)。
    *   从 `connections` map 中删除连接。

## 4. 流程图 (Mermaid)

```mermaid
graph TD
    A[启动 EpollServer] --> B{创建监听器和Epoll实例};
    B --> C[将监听器FD添加到Epoll];
    C --> D[进入Epoll事件循环];
    D -- 新连接事件 --> E[处理新连接 (handleAccept)];
    E --> F[接受连接];
    F --> G[设置非阻塞];
    G --> H[创建Connection对象];
    H --> I[将新连接FD添加到Epoll (EPOLLIN|EPOLLET)];
    I --> J[调用EventHandler.OnConnect];
    D -- 数据可读/连接关闭事件 --> K[处理读写事件 (handleReadWrite)];
    K --> L{从连接读取数据};
    L -- 读取到数据 --> M[调用EventHandler.OnRead];
    L -- 读取到0字节 --> N[调用EventHandler.OnClose];
    N --> O[从Epoll删除FD并关闭连接];
    L -- 错误 (非EAGAIN/EWOULDBLOCK) --> P[调用EventHandler.OnError];
    P --> O;
    O --> D;
    J --> D;
    M --> D;
```

## 5. 考虑事项和最佳实践

*   **错误处理：** 详细的错误日志和适当的错误恢复机制。
*   **缓冲区管理：** 使用 `sync.Pool` 来复用缓冲区，减少内存分配和垃圾回收压力。
*   **并发：** Epoll 本身是单线程的事件通知机制，但事件处理可以分发到 Goroutine 池中，以实现真正的并发处理。对于每个 `OnRead` 事件，可以启动一个新的 Goroutine 来处理业务逻辑，避免阻塞 Epoll 循环。
*   **优雅关闭：** 实现信号处理，以便在接收到 `SIGINT` 或 `SIGTERM` 等信号时，能够优雅地关闭服务器，包括关闭所有活跃连接和 Epoll 实例。
*   **心跳机制：** 对于长时间不活跃的连接，可以实现心跳机制来检测死连接并及时关闭。
*   **限流/熔断：** 在高并发场景下，考虑添加限流和熔断机制来保护服务器。
*   **日志：** 详细的日志记录，便于调试和监控。