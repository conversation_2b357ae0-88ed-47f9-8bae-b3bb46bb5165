package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	paymentConfig "pay-mall/services/payment-service/internal/config"
	"pay-mall/services/payment-service/internal/handler"
	"pay-mall/services/payment-service/internal/model"
	"pay-mall/services/payment-service/internal/provider"
	"pay-mall/services/payment-service/internal/repository"
	"pay-mall/services/payment-service/internal/router"
	"pay-mall/services/payment-service/internal/service"

	"github.com/stretchr/testify/assert"
)

// MockDB 模拟数据库
type MockDB struct{}

func TestPaymentServiceIntegration(t *testing.T) {
	// 创建模拟配置
	cfg := &paymentConfig.Config{
		Payment: paymentConfig.PaymentConfig{
			DefaultExpiresIn: 1800,
			Alipay: paymentConfig.AlipayConfig{
				AppID:      "test-app-id",
				PrivateKey: "test-private-key",
				PublicKey:  "test-public-key",
				GatewayURL: "https://test.alipay.com",
				NotifyURL:  "http://localhost:8084/api/v1/callbacks/alipay",
				ReturnURL:  "http://localhost:8084/api/v1/payment/success",
			},
		},
	}

	// 创建模拟仓储层
	paymentRepo := repository.NewMockPaymentRepository()
	callbackRepo := repository.NewMockPaymentCallbackRepository()

	// 创建支付提供商管理器
	providerManager := provider.NewProviderManager()
	providerManager.RegisterProvider(
		"ALIPAY",
		provider.NewAlipayProvider(cfg.Payment.Alipay),
	)

	// 创建服务层
	paymentService := service.NewPaymentService(
		paymentRepo,
		callbackRepo,
		providerManager,
		nil, // 暂时不使用事件服务
		cfg.Payment.DefaultExpiresIn,
	)

	// 创建处理器
	paymentHandler := handler.NewPaymentHandler(paymentService)

	// 创建路由
	r := router.SetupRouter(paymentHandler)

	t.Run("Health Check", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "ok", response["status"])
	})

	t.Run("Create Payment", func(t *testing.T) {
		paymentReq := model.PaymentCreateRequest{
			OrderID:       "order-123",
			UserID:        "user-123",
			Amount:        100.00,
			Currency:      "CNY",
			PaymentMethod: model.PaymentMethodAlipay,
			Description:   "Test payment",
			ExpiresIn:     1800,
		}

		reqBody, _ := json.Marshal(paymentReq)
		req, _ := http.NewRequest("POST", "/api/v1/payments", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response model.PaymentResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "order-123", response.OrderID)
		assert.Equal(t, "user-123", response.UserID)
		assert.Equal(t, 100.00, response.Amount)
		assert.Equal(t, model.PaymentStatusPending, response.Status)
	})

	t.Run("Get Payment by Order ID", func(t *testing.T) {
		// 先创建一个支付
		paymentReq := model.PaymentCreateRequest{
			OrderID:       "order-456",
			UserID:        "user-456",
			Amount:        200.00,
			Currency:      "CNY",
			PaymentMethod: model.PaymentMethodWechat,
			Description:   "Test payment 2",
			ExpiresIn:     1800,
		}

		reqBody, _ := json.Marshal(paymentReq)
		req, _ := http.NewRequest("POST", "/api/v1/payments", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		assert.Equal(t, http.StatusCreated, w.Code)

		// 通过订单ID查询支付
		req, _ = http.NewRequest("GET", "/api/v1/payments/order/order-456", nil)
		w = httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response model.PaymentResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "order-456", response.OrderID)
		assert.Equal(t, "user-456", response.UserID)
		assert.Equal(t, 200.00, response.Amount)
	})

	t.Run("Get Payments by User ID", func(t *testing.T) {
		// 查询用户的支付记录
		req, _ := http.NewRequest("GET", "/api/v1/payments/user/user-123", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response struct {
			Payments []model.PaymentResponse `json:"payments"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response.Payments, 1) // 应该有一个支付记录
		assert.Equal(t, "user-123", response.Payments[0].UserID)
	})
}
