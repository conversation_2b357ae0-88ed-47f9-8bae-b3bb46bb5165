package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	paymentConfig "pay-mall/services/payment-service/internal/config"
	"pay-mall/services/payment-service/internal/handler"
	"pay-mall/services/payment-service/internal/provider"
	"pay-mall/services/payment-service/internal/repository"
	"pay-mall/services/payment-service/internal/router"
	"pay-mall/services/payment-service/internal/service"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg, err := paymentConfig.LoadConfig("configs/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := connectDB(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 初始化仓储层
	paymentRepo := repository.NewPaymentRepository(db)
	callbackRepo := repository.NewPaymentCallbackRepository(db)

	// 初始化支付提供商
	providerManager := provider.NewProviderManager()
	providerManager.RegisterProvider(
		"ALIPAY",
		provider.NewAlipayProvider(cfg.Payment.Alipay),
	)
	providerManager.RegisterProvider(
		"WECHAT",
		provider.NewWechatProvider(cfg.Payment.Wechat),
	)
	providerManager.RegisterProvider(
		"BANK_CARD",
		provider.NewBankCardProvider(cfg.Payment.BankCard),
	)

	// 初始化服务层 (暂时不使用事件服务)
	paymentService := service.NewPaymentService(
		paymentRepo,
		callbackRepo,
		providerManager,
		nil, // eventService 暂时为 nil
		cfg.Payment.DefaultExpiresIn,
	)

	// 初始化处理器
	paymentHandler := handler.NewPaymentHandler(paymentService)

	// 设置路由
	r := router.SetupRouter(paymentHandler)

	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.App.Port),
		Handler: r,
	}

	go func() {
		log.Printf("Payment service starting on port %d", cfg.App.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}

// connectDB 连接数据库
func connectDB(cfg paymentConfig.DatabaseConfig) (*sql.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	log.Println("Connected to database successfully")
	return db, nil
}
