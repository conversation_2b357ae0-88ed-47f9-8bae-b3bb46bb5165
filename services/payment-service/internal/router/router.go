package router

import (
	"pay-mall/services/payment-service/internal/handler"
	"pay-mall/services/payment-service/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(paymentHandler *handler.PaymentHandler) *gin.Engine {
	r := gin.Default()
	
	// 添加中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())
	
	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})
	
	// API 路由组
	api := r.Group("/api/v1")
	{
		// 支付相关路由
		payments := api.Group("/payments")
		{
			// 创建支付
			payments.POST("", paymentHandler.CreatePayment)
			
			// 获取支付信息
			payments.GET("/:id", paymentHandler.GetPayment)
			
			// 发起支付
			payments.POST("/:id/pay", paymentHandler.InitiatePayment)
			
			// 根据订单ID获取支付信息
			payments.GET("/order/:order_id", paymentHandler.GetPaymentByOrderID)
			
			// 根据用户ID获取支付列表
			payments.GET("/user/:user_id", paymentHandler.GetPaymentsByUserID)
		}
		
		// 支付回调路由
		callbacks := api.Group("/callbacks")
		{
			// 支付宝回调
			callbacks.POST("/alipay/:payment_id", paymentHandler.HandleAlipayCallback)
			callbacks.GET("/alipay/:payment_id", paymentHandler.HandleAlipayCallback)
			
			// 微信支付回调
			callbacks.POST("/wechat/:payment_id", paymentHandler.HandleWechatCallback)
			callbacks.GET("/wechat/:payment_id", paymentHandler.HandleWechatCallback)
			
			// 银行卡支付回调
			callbacks.POST("/bankcard/:payment_id", paymentHandler.HandleBankCardCallback)
			callbacks.GET("/bankcard/:payment_id", paymentHandler.HandleBankCardCallback)
		}
		
		// 支付返回页面
		api.GET("/payment/success", paymentHandler.HandlePaymentReturn)
	}
	
	return r
}
