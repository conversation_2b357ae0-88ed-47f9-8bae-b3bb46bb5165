package db

import (
	"context"
	"database/sql"
)

// Queries 数据库查询接口
type Queries struct {
	db *sql.DB
}

// New 创建新的查询实例
func New(db *sql.DB) *Queries {
	return &Queries{db: db}
}

// CreatePaymentParams 创建支付参数
type CreatePaymentParams struct {
	ID            string                `json:"id"`
	OrderID       string                `json:"order_id"`
	UserID        string                `json:"user_id"`
	Amount        float64               `json:"amount"`
	Currency      string                `json:"currency"`
	PaymentMethod PaymentsPaymentMethod `json:"payment_method"`
	Status        PaymentsStatus        `json:"status"`
	CallbackUrl   sql.NullString        `json:"callback_url"`
	ReturnUrl     sql.NullString        `json:"return_url"`
	Description   sql.NullString        `json:"description"`
	Metadata      sql.NullString        `json:"metadata"`
	ExpiresAt     sql.NullTime          `json:"expires_at"`
}

// CreatePayment 创建支付记录
func (q *Queries) CreatePayment(ctx context.Context, arg CreatePaymentParams) error {
	query := `INSERT INTO payments (
		id, order_id, user_id, amount, currency, payment_method, 
		status, callback_url, return_url, description, metadata, expires_at
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	
	_, err := q.db.ExecContext(ctx, query,
		arg.ID,
		arg.OrderID,
		arg.UserID,
		arg.Amount,
		arg.Currency,
		arg.PaymentMethod,
		arg.Status,
		arg.CallbackUrl,
		arg.ReturnUrl,
		arg.Description,
		arg.Metadata,
		arg.ExpiresAt,
	)
	return err
}

// GetPaymentByID 根据ID获取支付记录
func (q *Queries) GetPaymentByID(ctx context.Context, id string) (*Payment, error) {
	query := `SELECT * FROM payments WHERE id = ?`
	
	row := q.db.QueryRowContext(ctx, query, id)
	var payment Payment
	err := row.Scan(
		&payment.ID,
		&payment.OrderID,
		&payment.UserID,
		&payment.Amount,
		&payment.Currency,
		&payment.PaymentMethod,
		&payment.Status,
		&payment.ExternalPaymentID,
		&payment.CallbackUrl,
		&payment.ReturnUrl,
		&payment.Description,
		&payment.Metadata,
		&payment.ExpiresAt,
		&payment.PaidAt,
		&payment.CreatedAt,
		&payment.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	return &payment, nil
}

// GetPaymentByOrderID 根据订单ID获取支付记录
func (q *Queries) GetPaymentByOrderID(ctx context.Context, orderID string) (*Payment, error) {
	query := `SELECT * FROM payments WHERE order_id = ? ORDER BY created_at DESC LIMIT 1`
	
	row := q.db.QueryRowContext(ctx, query, orderID)
	var payment Payment
	err := row.Scan(
		&payment.ID,
		&payment.OrderID,
		&payment.UserID,
		&payment.Amount,
		&payment.Currency,
		&payment.PaymentMethod,
		&payment.Status,
		&payment.ExternalPaymentID,
		&payment.CallbackUrl,
		&payment.ReturnUrl,
		&payment.Description,
		&payment.Metadata,
		&payment.ExpiresAt,
		&payment.PaidAt,
		&payment.CreatedAt,
		&payment.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	return &payment, nil
}

// GetPaymentsByUserID 根据用户ID获取支付记录列表
func (q *Queries) GetPaymentsByUserID(ctx context.Context, userID string) ([]Payment, error) {
	query := `SELECT * FROM payments WHERE user_id = ? ORDER BY created_at DESC`
	
	rows, err := q.db.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var payments []Payment
	for rows.Next() {
		var payment Payment
		err := rows.Scan(
			&payment.ID,
			&payment.OrderID,
			&payment.UserID,
			&payment.Amount,
			&payment.Currency,
			&payment.PaymentMethod,
			&payment.Status,
			&payment.ExternalPaymentID,
			&payment.CallbackUrl,
			&payment.ReturnUrl,
			&payment.Description,
			&payment.Metadata,
			&payment.ExpiresAt,
			&payment.PaidAt,
			&payment.CreatedAt,
			&payment.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		payments = append(payments, payment)
	}
	
	return payments, nil
}

// UpdatePaymentStatusParams 更新支付状态参数
type UpdatePaymentStatusParams struct {
	Status            PaymentsStatus `json:"status"`
	ExternalPaymentID sql.NullString `json:"external_payment_id"`
	PaidAt            sql.NullTime   `json:"paid_at"`
	ID                string         `json:"id"`
}

// UpdatePaymentStatus 更新支付状态
func (q *Queries) UpdatePaymentStatus(ctx context.Context, arg UpdatePaymentStatusParams) error {
	query := `UPDATE payments 
		SET status = ?, external_payment_id = ?, paid_at = ?, updated_at = CURRENT_TIMESTAMP 
		WHERE id = ?`
	
	_, err := q.db.ExecContext(ctx, query,
		arg.Status,
		arg.ExternalPaymentID,
		arg.PaidAt,
		arg.ID,
	)
	return err
}

// UpdatePaymentExternalIDParams 更新外部支付ID参数
type UpdatePaymentExternalIDParams struct {
	ExternalPaymentID sql.NullString `json:"external_payment_id"`
	ID                string         `json:"id"`
}

// UpdatePaymentExternalID 更新外部支付ID
func (q *Queries) UpdatePaymentExternalID(ctx context.Context, arg UpdatePaymentExternalIDParams) error {
	query := `UPDATE payments 
		SET external_payment_id = ?, updated_at = CURRENT_TIMESTAMP 
		WHERE id = ?`
	
	_, err := q.db.ExecContext(ctx, query,
		arg.ExternalPaymentID,
		arg.ID,
	)
	return err
}

// GetExpiredPayments 获取过期的支付记录
func (q *Queries) GetExpiredPayments(ctx context.Context) ([]Payment, error) {
	query := `SELECT * FROM payments 
		WHERE status = 'PENDING' AND expires_at < NOW()`
	
	rows, err := q.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var payments []Payment
	for rows.Next() {
		var payment Payment
		err := rows.Scan(
			&payment.ID,
			&payment.OrderID,
			&payment.UserID,
			&payment.Amount,
			&payment.Currency,
			&payment.PaymentMethod,
			&payment.Status,
			&payment.ExternalPaymentID,
			&payment.CallbackUrl,
			&payment.ReturnUrl,
			&payment.Description,
			&payment.Metadata,
			&payment.ExpiresAt,
			&payment.PaidAt,
			&payment.CreatedAt,
			&payment.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		payments = append(payments, payment)
	}
	
	return payments, nil
}

// CreatePaymentCallbackParams 创建支付回调参数
type CreatePaymentCallbackParams struct {
	ID           string                       `json:"id"`
	PaymentID    string                       `json:"payment_id"`
	CallbackType PaymentCallbacksCallbackType `json:"callback_type"`
	RawData      string                       `json:"raw_data"`
}

// CreatePaymentCallback 创建支付回调记录
func (q *Queries) CreatePaymentCallback(ctx context.Context, arg CreatePaymentCallbackParams) error {
	query := `INSERT INTO payment_callbacks (
		id, payment_id, callback_type, raw_data
	) VALUES (?, ?, ?, ?)`
	
	_, err := q.db.ExecContext(ctx, query,
		arg.ID,
		arg.PaymentID,
		arg.CallbackType,
		arg.RawData,
	)
	return err
}

// GetPaymentCallbacks 获取支付回调记录
func (q *Queries) GetPaymentCallbacks(ctx context.Context, paymentID string) ([]PaymentCallback, error) {
	query := `SELECT * FROM payment_callbacks 
		WHERE payment_id = ? ORDER BY created_at DESC`
	
	rows, err := q.db.QueryContext(ctx, query, paymentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var callbacks []PaymentCallback
	for rows.Next() {
		var callback PaymentCallback
		err := rows.Scan(
			&callback.ID,
			&callback.PaymentID,
			&callback.CallbackType,
			&callback.RawData,
			&callback.Processed,
			&callback.ProcessedAt,
			&callback.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		callbacks = append(callbacks, callback)
	}
	
	return callbacks, nil
}

// MarkCallbackProcessed 标记回调已处理
func (q *Queries) MarkCallbackProcessed(ctx context.Context, id string) error {
	query := `UPDATE payment_callbacks 
		SET processed = TRUE, processed_at = CURRENT_TIMESTAMP 
		WHERE id = ?`
	
	_, err := q.db.ExecContext(ctx, query, id)
	return err
}

// GetUnprocessedCallbacks 获取未处理的回调
func (q *Queries) GetUnprocessedCallbacks(ctx context.Context) ([]PaymentCallback, error) {
	query := `SELECT * FROM payment_callbacks 
		WHERE processed = FALSE ORDER BY created_at ASC`
	
	rows, err := q.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var callbacks []PaymentCallback
	for rows.Next() {
		var callback PaymentCallback
		err := rows.Scan(
			&callback.ID,
			&callback.PaymentID,
			&callback.CallbackType,
			&callback.RawData,
			&callback.Processed,
			&callback.ProcessedAt,
			&callback.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		callbacks = append(callbacks, callback)
	}
	
	return callbacks, nil
}
