package db

import (
	"database/sql"
	"time"
)

// Payment 支付记录
type Payment struct {
	ID                string         `json:"id" db:"id"`
	OrderID           string         `json:"order_id" db:"order_id"`
	UserID            string         `json:"user_id" db:"user_id"`
	Amount            float64        `json:"amount" db:"amount"`
	Currency          string         `json:"currency" db:"currency"`
	PaymentMethod     PaymentsPaymentMethod `json:"payment_method" db:"payment_method"`
	Status            PaymentsStatus `json:"status" db:"status"`
	ExternalPaymentID sql.NullString `json:"external_payment_id" db:"external_payment_id"`
	CallbackUrl       sql.NullString `json:"callback_url" db:"callback_url"`
	ReturnUrl         sql.NullString `json:"return_url" db:"return_url"`
	Description       sql.NullString `json:"description" db:"description"`
	Metadata          sql.NullString `json:"metadata" db:"metadata"`
	ExpiresAt         sql.NullTime   `json:"expires_at" db:"expires_at"`
	PaidAt            sql.NullTime   `json:"paid_at" db:"paid_at"`
	CreatedAt         time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at" db:"updated_at"`
}

// PaymentCallback 支付回调记录
type PaymentCallback struct {
	ID           string                         `json:"id" db:"id"`
	PaymentID    string                         `json:"payment_id" db:"payment_id"`
	CallbackType PaymentCallbacksCallbackType   `json:"callback_type" db:"callback_type"`
	RawData      string                         `json:"raw_data" db:"raw_data"`
	Processed    bool                           `json:"processed" db:"processed"`
	ProcessedAt  sql.NullTime                   `json:"processed_at" db:"processed_at"`
	CreatedAt    time.Time                      `json:"created_at" db:"created_at"`
}

// PaymentsPaymentMethod 支付方式枚举
type PaymentsPaymentMethod string

const (
	PaymentsPaymentMethodALIPAY   PaymentsPaymentMethod = "ALIPAY"
	PaymentsPaymentMethodWECHAT   PaymentsPaymentMethod = "WECHAT"
	PaymentsPaymentMethodBANKCARD PaymentsPaymentMethod = "BANK_CARD"
)

// PaymentsStatus 支付状态枚举
type PaymentsStatus string

const (
	PaymentsStatusPENDING   PaymentsStatus = "PENDING"
	PaymentsStatusSUCCESS   PaymentsStatus = "SUCCESS"
	PaymentsStatusFAILED    PaymentsStatus = "FAILED"
	PaymentsStatusCANCELLED PaymentsStatus = "CANCELLED"
	PaymentsStatusREFUNDED  PaymentsStatus = "REFUNDED"
)

// PaymentCallbacksCallbackType 回调类型枚举
type PaymentCallbacksCallbackType string

const (
	PaymentCallbacksCallbackTypeNOTIFY PaymentCallbacksCallbackType = "NOTIFY"
	PaymentCallbacksCallbackTypeRETURN PaymentCallbacksCallbackType = "RETURN"
)
