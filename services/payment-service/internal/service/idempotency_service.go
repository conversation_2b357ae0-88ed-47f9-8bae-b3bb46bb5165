package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// IdempotencyService 幂等性服务接口
type IdempotencyService interface {
	// CheckAndSet 检查并设置幂等键，如果已存在返回 false
	CheckAndSet(ctx context.Context, key string, value interface{}, ttl time.Duration) (bool, error)
	// Get 获取幂等键对应的值
	Get(ctx context.Context, key string) (string, error)
	// Delete 删除幂等键
	Delete(ctx context.Context, key string) error
	// GeneratePaymentKey 生成支付幂等键
	GeneratePaymentKey(orderID, userID string, amount float64) string
	// GenerateCallbackKey 生成回调幂等键
	GenerateCallbackKey(paymentID, callbackType, rawData string) string
}

// idempotencyService 幂等性服务实现
type idempotencyService struct {
	redis *redis.Client
}

// NewIdempotencyService 创建幂等性服务
func NewIdempotencyService(redisClient *redis.Client) IdempotencyService {
	return &idempotencyService{
		redis: redisClient,
	}
}

// CheckAndSet 检查并设置幂等键
func (s *idempotencyService) CheckAndSet(ctx context.Context, key string, value interface{}, ttl time.Duration) (bool, error) {
	// 使用 SET NX EX 命令，原子性地检查并设置
	result := s.redis.SetNX(ctx, key, value, ttl)
	if result.Err() != nil {
		return false, fmt.Errorf("failed to set idempotency key: %w", result.Err())
	}

	return result.Val(), nil
}

// Get 获取幂等键对应的值
func (s *idempotencyService) Get(ctx context.Context, key string) (string, error) {
	result := s.redis.Get(ctx, key)
	if result.Err() == redis.Nil {
		return "", nil // 键不存在
	}
	if result.Err() != nil {
		return "", fmt.Errorf("failed to get idempotency key: %w", result.Err())
	}

	return result.Val(), nil
}

// Delete 删除幂等键
func (s *idempotencyService) Delete(ctx context.Context, key string) error {
	result := s.redis.Del(ctx, key)
	if result.Err() != nil {
		return fmt.Errorf("failed to delete idempotency key: %w", result.Err())
	}

	return nil
}

// GeneratePaymentKey 生成支付幂等键
func (s *idempotencyService) GeneratePaymentKey(orderID, userID string, amount float64) string {
	// 使用订单ID、用户ID、金额生成唯一键
	data := fmt.Sprintf("payment:%s:%s:%.2f", orderID, userID, amount)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("idempotency:payment:%x", hash)
}

// GenerateCallbackKey 生成回调幂等键
func (s *idempotencyService) GenerateCallbackKey(paymentID, callbackType, rawData string) string {
	// 使用支付ID、回调类型、原始数据生成唯一键
	data := fmt.Sprintf("callback:%s:%s:%s", paymentID, callbackType, rawData)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("idempotency:callback:%x", hash)
}

// IdempotencyResult 幂等性检查结果
type IdempotencyResult struct {
	IsNew bool        // 是否是新请求
	Value interface{} // 如果是重复请求，返回之前的结果
	Key   string      // 幂等键
}

// CheckPaymentIdempotency 检查支付幂等性
func CheckPaymentIdempotency(ctx context.Context, idempotencyService IdempotencyService, orderID, userID string, amount float64) (*IdempotencyResult, error) {
	key := idempotencyService.GeneratePaymentKey(orderID, userID, amount)

	// 检查是否已存在
	existingValue, err := idempotencyService.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to check idempotency: %w", err)
	}

	if existingValue != "" {
		// 已存在，返回重复请求
		return &IdempotencyResult{
			IsNew: false,
			Value: existingValue,
			Key:   key,
		}, nil
	}

	// 新请求
	return &IdempotencyResult{
		IsNew: true,
		Key:   key,
	}, nil
}

// SetPaymentIdempotency 设置支付幂等性
func SetPaymentIdempotency(ctx context.Context, idempotencyService IdempotencyService, key string, paymentID string) error {
	// 设置 24 小时过期时间
	ttl := 24 * time.Hour

	success, err := idempotencyService.CheckAndSet(ctx, key, paymentID, ttl)
	if err != nil {
		return fmt.Errorf("failed to set payment idempotency: %w", err)
	}

	if !success {
		return fmt.Errorf("payment idempotency key already exists")
	}

	return nil
}

// CheckCallbackIdempotency 检查回调幂等性
func CheckCallbackIdempotency(ctx context.Context, idempotencyService IdempotencyService, paymentID, callbackType, rawData string) (*IdempotencyResult, error) {
	key := idempotencyService.GenerateCallbackKey(paymentID, callbackType, rawData)

	// 检查是否已存在
	existingValue, err := idempotencyService.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to check callback idempotency: %w", err)
	}

	if existingValue != "" {
		// 已存在，返回重复请求
		return &IdempotencyResult{
			IsNew: false,
			Value: existingValue,
			Key:   key,
		}, nil
	}

	// 新请求
	return &IdempotencyResult{
		IsNew: true,
		Key:   key,
	}, nil
}

// SetCallbackIdempotency 设置回调幂等性
func SetCallbackIdempotency(ctx context.Context, idempotencyService IdempotencyService, key string, result string) error {
	// 设置 1 小时过期时间（回调处理完成后不需要长期保存）
	ttl := 1 * time.Hour

	success, err := idempotencyService.CheckAndSet(ctx, key, result, ttl)
	if err != nil {
		return fmt.Errorf("failed to set callback idempotency: %w", err)
	}

	if !success {
		return fmt.Errorf("callback idempotency key already exists")
	}

	return nil
}
