package handler

import (
	"net/http"
	"pay-mall/services/payment-service/internal/model"
	"pay-mall/services/payment-service/internal/service"

	"github.com/gin-gonic/gin"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService service.PaymentService
}

// NewPaymentHandler 创建支付处理器
func NewPaymentHandler(paymentService service.PaymentService) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
	}
}

// CreatePayment 创建支付
func (h *PaymentHandler) CreatePayment(c *gin.Context) {
	var req model.PaymentCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	payment, err := h.paymentService.CreatePayment(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSO<PERSON>(http.StatusCreated, payment)
}

// GetPayment 获取支付信息
func (h *PaymentHandler) GetPayment(c *gin.Context) {
	paymentID := c.Param("id")
	if paymentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "payment ID is required"})
		return
	}
	
	payment, err := h.paymentService.GetPayment(c.Request.Context(), paymentID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "payment not found"})
		return
	}
	
	c.JSON(http.StatusOK, payment)
}

// GetPaymentByOrderID 根据订单ID获取支付信息
func (h *PaymentHandler) GetPaymentByOrderID(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "order ID is required"})
		return
	}
	
	payment, err := h.paymentService.GetPaymentByOrderID(c.Request.Context(), orderID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "payment not found"})
		return
	}
	
	c.JSON(http.StatusOK, payment)
}

// GetPaymentsByUserID 根据用户ID获取支付列表
func (h *PaymentHandler) GetPaymentsByUserID(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user ID is required"})
		return
	}
	
	payments, err := h.paymentService.GetPaymentsByUserID(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"payments": payments})
}

// InitiatePayment 发起支付
func (h *PaymentHandler) InitiatePayment(c *gin.Context) {
	paymentID := c.Param("id")
	if paymentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "payment ID is required"})
		return
	}
	
	var req model.PaymentInitiateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	response, err := h.paymentService.InitiatePayment(c.Request.Context(), paymentID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, response)
}

// HandleAlipayCallback 处理支付宝回调
func (h *PaymentHandler) HandleAlipayCallback(c *gin.Context) {
	h.handleCallback(c, model.PaymentMethodAlipay)
}

// HandleWechatCallback 处理微信支付回调
func (h *PaymentHandler) HandleWechatCallback(c *gin.Context) {
	h.handleCallback(c, model.PaymentMethodWechat)
}

// HandleBankCardCallback 处理银行卡支付回调
func (h *PaymentHandler) HandleBankCardCallback(c *gin.Context) {
	h.handleCallback(c, model.PaymentMethodBankCard)
}

// handleCallback 处理支付回调的通用方法
func (h *PaymentHandler) handleCallback(c *gin.Context, paymentMethod model.PaymentMethod) {
	paymentID := c.Param("payment_id")
	if paymentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "payment ID is required"})
		return
	}
	
	// 获取回调类型
	callbackType := model.CallbackTypeNotify
	if c.Query("type") == "return" {
		callbackType = model.CallbackTypeReturn
	}
	
	// 获取原始数据
	rawData := ""
	if c.Request.Method == "POST" {
		// POST 请求，从 body 获取数据
		body, err := c.GetRawData()
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "failed to read request body"})
			return
		}
		rawData = string(body)
	} else {
		// GET 请求，从查询参数获取数据
		rawData = c.Request.URL.RawQuery
	}
	
	// 处理回调
	err := h.paymentService.HandleCallback(c.Request.Context(), paymentID, callbackType, rawData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// 根据回调类型返回不同响应
	if callbackType == model.CallbackTypeNotify {
		// 异步通知回调，返回成功标识
		c.String(http.StatusOK, "success")
	} else {
		// 同步返回回调，重定向到成功页面
		c.Redirect(http.StatusFound, "/payment/success?payment_id="+paymentID)
	}
}

// HandlePaymentReturn 处理支付返回页面
func (h *PaymentHandler) HandlePaymentReturn(c *gin.Context) {
	paymentID := c.Query("payment_id")
	if paymentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "payment ID is required"})
		return
	}
	
	payment, err := h.paymentService.GetPayment(c.Request.Context(), paymentID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "payment not found"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Payment processed",
		"payment": payment,
	})
}
