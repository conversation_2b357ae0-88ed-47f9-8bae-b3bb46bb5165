package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// PaymentMethod 支付方式枚举
type PaymentMethod string

const (
	PaymentMethodAlipay   PaymentMethod = "ALIPAY"
	PaymentMethodWechat   PaymentMethod = "WECHAT"
	PaymentMethodBankCard PaymentMethod = "BANK_CARD"
)

// PaymentStatus 支付状态枚举
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "PENDING"
	PaymentStatusSuccess   PaymentStatus = "SUCCESS"
	PaymentStatusFailed    PaymentStatus = "FAILED"
	PaymentStatusCancelled PaymentStatus = "CANCELLED"
	PaymentStatusRefunded  PaymentStatus = "REFUNDED"
)

// CallbackType 回调类型枚举
type CallbackType string

const (
	CallbackTypeNotify CallbackType = "NOTIFY"
	CallbackTypeReturn CallbackType = "RETURN"
)

// PaymentCreateRequest 创建支付请求
type PaymentCreateRequest struct {
	OrderID       string            `json:"order_id" binding:"required"`
	UserID        string            `json:"user_id" binding:"required"`
	Amount        float64           `json:"amount" binding:"required,gt=0"`
	Currency      string            `json:"currency" binding:"omitempty"`
	PaymentMethod PaymentMethod     `json:"payment_method" binding:"required,oneof=ALIPAY WECHAT BANK_CARD"`
	CallbackURL   string            `json:"callback_url" binding:"omitempty,url"`
	ReturnURL     string            `json:"return_url" binding:"omitempty,url"`
	Description   string            `json:"description" binding:"omitempty"`
	Metadata      map[string]string `json:"metadata" binding:"omitempty"`
	ExpiresIn     int               `json:"expires_in" binding:"omitempty,min=60"` // 过期时间（秒）
}

// PaymentResponse 支付响应
type PaymentResponse struct {
	ID                string            `json:"id"`
	OrderID           string            `json:"order_id"`
	UserID            string            `json:"user_id"`
	Amount            float64           `json:"amount"`
	Currency          string            `json:"currency"`
	PaymentMethod     PaymentMethod     `json:"payment_method"`
	Status            PaymentStatus     `json:"status"`
	ExternalPaymentID *string           `json:"external_payment_id"`
	CallbackURL       *string           `json:"callback_url"`
	ReturnURL         *string           `json:"return_url"`
	Description       *string           `json:"description"`
	Metadata          map[string]string `json:"metadata"`
	ExpiresAt         *time.Time        `json:"expires_at"`
	PaidAt            *time.Time        `json:"paid_at"`
	CreatedAt         time.Time         `json:"created_at"`
	UpdatedAt         time.Time         `json:"updated_at"`
}

// PaymentInitiateRequest 发起支付请求
type PaymentInitiateRequest struct {
	PaymentMethod PaymentMethod `json:"payment_method" binding:"required,oneof=ALIPAY WECHAT BANK_CARD"`
	ReturnURL     string        `json:"return_url" binding:"omitempty,url"`
}

// PaymentInitiateResponse 发起支付响应
type PaymentInitiateResponse struct {
	PaymentID   string      `json:"payment_id"`
	PaymentURL  string      `json:"payment_url,omitempty"`  // 支付页面URL
	QRCode      string      `json:"qr_code,omitempty"`      // 二维码内容
	PaymentData interface{} `json:"payment_data,omitempty"` // 支付相关数据
}

// PaymentCallbackRequest 支付回调请求
type PaymentCallbackRequest struct {
	PaymentID    string       `json:"payment_id" binding:"required"`
	CallbackType CallbackType `json:"callback_type" binding:"required,oneof=NOTIFY RETURN"`
	RawData      string       `json:"raw_data" binding:"required"`
}

// Scan 实现 sql.Scanner 接口
func (pm *PaymentMethod) Scan(value interface{}) error {
	if value == nil {
		*pm = PaymentMethodAlipay
		return nil
	}
	if str, ok := value.(string); ok {
		*pm = PaymentMethod(str)
		return nil
	}
	return fmt.Errorf("cannot scan %T into PaymentMethod", value)
}

// Value 实现 driver.Valuer 接口
func (pm PaymentMethod) Value() (driver.Value, error) {
	return string(pm), nil
}

// Scan 实现 sql.Scanner 接口
func (ps *PaymentStatus) Scan(value interface{}) error {
	if value == nil {
		*ps = PaymentStatusPending
		return nil
	}
	if str, ok := value.(string); ok {
		*ps = PaymentStatus(str)
		return nil
	}
	return fmt.Errorf("cannot scan %T into PaymentStatus", value)
}

// Value 实现 driver.Valuer 接口
func (ps PaymentStatus) Value() (driver.Value, error) {
	return string(ps), nil
}

// Scan 实现 sql.Scanner 接口
func (ct *CallbackType) Scan(value interface{}) error {
	if value == nil {
		*ct = CallbackTypeNotify
		return nil
	}
	if str, ok := value.(string); ok {
		*ct = CallbackType(str)
		return nil
	}
	return fmt.Errorf("cannot scan %T into CallbackType", value)
}

// Value 实现 driver.Valuer 接口
func (ct CallbackType) Value() (driver.Value, error) {
	return string(ct), nil
}

// MetadataMap 用于处理 JSON 字段
type MetadataMap map[string]string

// Scan 实现 sql.Scanner 接口
func (m *MetadataMap) Scan(value interface{}) error {
	if value == nil {
		*m = make(map[string]string)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into MetadataMap", value)
	}
	
	return json.Unmarshal(bytes, m)
}

// Value 实现 driver.Valuer 接口
func (m MetadataMap) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}
