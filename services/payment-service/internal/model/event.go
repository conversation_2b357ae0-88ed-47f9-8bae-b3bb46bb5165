package model

import "time"

// OrderCreatedEvent 订单创建事件
type OrderCreatedEvent struct {
	OrderID     string    `json:"order_id"`
	UserID      string    `json:"user_id"`
	TotalAmount float64   `json:"total_amount"`
	Currency    string    `json:"currency"`
	Items       []OrderItem `json:"items"`
	CreatedAt   time.Time `json:"created_at"`
}

// OrderItem 订单项
type OrderItem struct {
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Price     float64 `json:"price"`
	Name      string  `json:"name"`
}

// PaymentSuccessEvent 支付成功事件
type PaymentSuccessEvent struct {
	PaymentID         string    `json:"payment_id"`
	OrderID           string    `json:"order_id"`
	UserID            string    `json:"user_id"`
	Amount            float64   `json:"amount"`
	Currency          string    `json:"currency"`
	PaymentMethod     string    `json:"payment_method"`
	ExternalPaymentID string    `json:"external_payment_id"`
	PaidAt            time.Time `json:"paid_at"`
}

// PaymentFailedEvent 支付失败事件
type PaymentFailedEvent struct {
	PaymentID     string    `json:"payment_id"`
	OrderID       string    `json:"order_id"`
	UserID        string    `json:"user_id"`
	Amount        float64   `json:"amount"`
	Currency      string    `json:"currency"`
	PaymentMethod string    `json:"payment_method"`
	FailureReason string    `json:"failure_reason"`
	FailedAt      time.Time `json:"failed_at"`
}
