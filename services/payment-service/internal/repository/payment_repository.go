package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"pay-mall/services/payment-service/internal/db"
	"pay-mall/services/payment-service/internal/model"
	"time"

	"github.com/google/uuid"
)

// PaymentRepository 支付仓储接口
type PaymentRepository interface {
	Create(ctx context.Context, payment *model.PaymentCreateRequest) (*db.Payment, error)
	GetByID(ctx context.Context, id string) (*db.Payment, error)
	GetByOrderID(ctx context.Context, orderID string) (*db.Payment, error)
	GetByUserID(ctx context.Context, userID string) ([]db.Payment, error)
	UpdateStatus(ctx context.Context, id string, status model.PaymentStatus, externalPaymentID *string, paidAt *time.Time) error
	UpdateExternalID(ctx context.Context, id string, externalPaymentID string) error
	GetExpiredPayments(ctx context.Context) ([]db.Payment, error)
}

// PaymentCallbackRepository 支付回调仓储接口
type PaymentCallbackRepository interface {
	Create(ctx context.Context, callback *model.PaymentCallbackRequest) (*db.PaymentCallback, error)
	GetByPaymentID(ctx context.Context, paymentID string) ([]db.PaymentCallback, error)
	MarkProcessed(ctx context.Context, id string) error
	GetUnprocessed(ctx context.Context) ([]db.PaymentCallback, error)
}

// paymentRepository 支付仓储实现
type paymentRepository struct {
	db      *sql.DB
	queries *db.Queries
}

// NewPaymentRepository 创建支付仓储
func NewPaymentRepository(database *sql.DB) PaymentRepository {
	return &paymentRepository{
		db:      database,
		queries: db.New(database),
	}
}

// Create 创建支付记录
func (r *paymentRepository) Create(ctx context.Context, payment *model.PaymentCreateRequest) (*db.Payment, error) {
	id := uuid.New().String()
	
	// 设置默认货币
	currency := payment.Currency
	if currency == "" {
		currency = "CNY"
	}
	
	// 设置过期时间
	var expiresAt sql.NullTime
	if payment.ExpiresIn > 0 {
		expiresAt = sql.NullTime{
			Time:  time.Now().Add(time.Duration(payment.ExpiresIn) * time.Second),
			Valid: true,
		}
	}
	
	// 序列化元数据
	var metadataBytes []byte
	if payment.Metadata != nil {
		var err error
		metadataBytes, err = json.Marshal(payment.Metadata)
		if err != nil {
			return nil, err
		}
	}
	
	// 转换为 sql.Null 类型
	var callbackURL, returnURL, description sql.NullString
	var metadata sql.NullString
	
	if payment.CallbackURL != "" {
		callbackURL = sql.NullString{String: payment.CallbackURL, Valid: true}
	}
	if payment.ReturnURL != "" {
		returnURL = sql.NullString{String: payment.ReturnURL, Valid: true}
	}
	if payment.Description != "" {
		description = sql.NullString{String: payment.Description, Valid: true}
	}
	if metadataBytes != nil {
		metadata = sql.NullString{String: string(metadataBytes), Valid: true}
	}
	
	err := r.queries.CreatePayment(ctx, db.CreatePaymentParams{
		ID:            id,
		OrderID:       payment.OrderID,
		UserID:        payment.UserID,
		Amount:        payment.Amount,
		Currency:      currency,
		PaymentMethod: db.PaymentsPaymentMethod(payment.PaymentMethod),
		Status:        db.PaymentsStatus(model.PaymentStatusPending),
		CallbackUrl:   callbackURL,
		ReturnUrl:     returnURL,
		Description:   description,
		Metadata:      metadata,
		ExpiresAt:     expiresAt,
	})
	
	if err != nil {
		return nil, err
	}
	
	return r.GetByID(ctx, id)
}

// GetByID 根据ID获取支付记录
func (r *paymentRepository) GetByID(ctx context.Context, id string) (*db.Payment, error) {
	return r.queries.GetPaymentByID(ctx, id)
}

// GetByOrderID 根据订单ID获取支付记录
func (r *paymentRepository) GetByOrderID(ctx context.Context, orderID string) (*db.Payment, error) {
	return r.queries.GetPaymentByOrderID(ctx, orderID)
}

// GetByUserID 根据用户ID获取支付记录列表
func (r *paymentRepository) GetByUserID(ctx context.Context, userID string) ([]db.Payment, error) {
	return r.queries.GetPaymentsByUserID(ctx, userID)
}

// UpdateStatus 更新支付状态
func (r *paymentRepository) UpdateStatus(ctx context.Context, id string, status model.PaymentStatus, externalPaymentID *string, paidAt *time.Time) error {
	var externalID sql.NullString
	var paidTime sql.NullTime
	
	if externalPaymentID != nil {
		externalID = sql.NullString{String: *externalPaymentID, Valid: true}
	}
	
	if paidAt != nil {
		paidTime = sql.NullTime{Time: *paidAt, Valid: true}
	}
	
	return r.queries.UpdatePaymentStatus(ctx, db.UpdatePaymentStatusParams{
		Status:            db.PaymentsStatus(status),
		ExternalPaymentID: externalID,
		PaidAt:            paidTime,
		ID:                id,
	})
}

// UpdateExternalID 更新外部支付ID
func (r *paymentRepository) UpdateExternalID(ctx context.Context, id string, externalPaymentID string) error {
	return r.queries.UpdatePaymentExternalID(ctx, db.UpdatePaymentExternalIDParams{
		ExternalPaymentID: sql.NullString{String: externalPaymentID, Valid: true},
		ID:                id,
	})
}

// GetExpiredPayments 获取过期的支付记录
func (r *paymentRepository) GetExpiredPayments(ctx context.Context) ([]db.Payment, error) {
	return r.queries.GetExpiredPayments(ctx)
}

// paymentCallbackRepository 支付回调仓储实现
type paymentCallbackRepository struct {
	db      *sql.DB
	queries *db.Queries
}

// NewPaymentCallbackRepository 创建支付回调仓储
func NewPaymentCallbackRepository(database *sql.DB) PaymentCallbackRepository {
	return &paymentCallbackRepository{
		db:      database,
		queries: db.New(database),
	}
}

// Create 创建支付回调记录
func (r *paymentCallbackRepository) Create(ctx context.Context, callback *model.PaymentCallbackRequest) (*db.PaymentCallback, error) {
	id := uuid.New().String()
	
	err := r.queries.CreatePaymentCallback(ctx, db.CreatePaymentCallbackParams{
		ID:           id,
		PaymentID:    callback.PaymentID,
		CallbackType: db.PaymentCallbacksCallbackType(callback.CallbackType),
		RawData:      callback.RawData,
	})
	
	if err != nil {
		return nil, err
	}
	
	// 返回创建的记录（这里简化处理，实际可能需要查询）
	return &db.PaymentCallback{
		ID:           id,
		PaymentID:    callback.PaymentID,
		CallbackType: db.PaymentCallbacksCallbackType(callback.CallbackType),
		RawData:      callback.RawData,
		Processed:    false,
		CreatedAt:    time.Now(),
	}, nil
}

// GetByPaymentID 根据支付ID获取回调记录
func (r *paymentCallbackRepository) GetByPaymentID(ctx context.Context, paymentID string) ([]db.PaymentCallback, error) {
	return r.queries.GetPaymentCallbacks(ctx, paymentID)
}

// MarkProcessed 标记回调已处理
func (r *paymentCallbackRepository) MarkProcessed(ctx context.Context, id string) error {
	return r.queries.MarkCallbackProcessed(ctx, id)
}

// GetUnprocessed 获取未处理的回调
func (r *paymentCallbackRepository) GetUnprocessed(ctx context.Context) ([]db.PaymentCallback, error) {
	return r.queries.GetUnprocessedCallbacks(ctx)
}
