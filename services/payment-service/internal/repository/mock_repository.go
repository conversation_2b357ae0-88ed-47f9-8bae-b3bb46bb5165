package repository

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"pay-mall/services/payment-service/internal/db"
	"pay-mall/services/payment-service/internal/model"

	"github.com/google/uuid"
)

// MockPaymentRepository 模拟支付仓储
type MockPaymentRepository struct {
	payments map[string]*db.Payment
	mutex    sync.RWMutex
}

// NewMockPaymentRepository 创建模拟支付仓储
func NewMockPaymentRepository() PaymentRepository {
	return &MockPaymentRepository{
		payments: make(map[string]*db.Payment),
	}
}

func (r *MockPaymentRepository) Create(ctx context.Context, req *model.PaymentCreateRequest) (*db.Payment, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	id := uuid.New().String()
	now := time.Now()

	payment := &db.Payment{
		ID:            id,
		OrderID:       req.OrderID,
		UserID:        req.UserID,
		Amount:        req.Amount,
		Currency:      req.Currency,
		PaymentMethod: db.PaymentsPaymentMethod(req.PaymentMethod),
		Status:        db.PaymentsStatusPENDING,
		CallbackUrl:   sql.NullString{String: req.CallbackURL, Valid: req.CallbackURL != ""},
		ReturnUrl:     sql.NullString{String: req.ReturnURL, Valid: req.ReturnURL != ""},
		Description:   sql.NullString{String: req.Description, Valid: req.Description != ""},
		ExpiresAt:     sql.NullTime{Time: now.Add(time.Duration(req.ExpiresIn) * time.Second), Valid: true},
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	r.payments[payment.ID] = payment
	return payment, nil
}

func (r *MockPaymentRepository) GetByID(ctx context.Context, id string) (*db.Payment, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	payment, exists := r.payments[id]
	if !exists {
		return nil, fmt.Errorf("payment not found")
	}

	return payment, nil
}

func (r *MockPaymentRepository) GetByOrderID(ctx context.Context, orderID string) (*db.Payment, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	for _, payment := range r.payments {
		if payment.OrderID == orderID {
			return payment, nil
		}
	}

	return nil, fmt.Errorf("payment not found")
}

func (r *MockPaymentRepository) GetByUserID(ctx context.Context, userID string) ([]db.Payment, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var payments []db.Payment
	for _, payment := range r.payments {
		if payment.UserID == userID {
			payments = append(payments, *payment)
		}
	}

	return payments, nil
}

func (r *MockPaymentRepository) UpdateStatus(ctx context.Context, id string, status model.PaymentStatus, externalPaymentID *string, paidAt *time.Time) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	payment, exists := r.payments[id]
	if !exists {
		return fmt.Errorf("payment not found")
	}

	payment.Status = db.PaymentsStatus(status)
	if externalPaymentID != nil {
		payment.ExternalPaymentID = sql.NullString{String: *externalPaymentID, Valid: true}
	}
	if paidAt != nil {
		payment.PaidAt = sql.NullTime{Time: *paidAt, Valid: true}
	}
	payment.UpdatedAt = time.Now()

	return nil
}

func (r *MockPaymentRepository) UpdateExternalID(ctx context.Context, id string, externalPaymentID string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	payment, exists := r.payments[id]
	if !exists {
		return fmt.Errorf("payment not found")
	}

	payment.ExternalPaymentID = sql.NullString{String: externalPaymentID, Valid: true}
	payment.UpdatedAt = time.Now()

	return nil
}

func (r *MockPaymentRepository) GetExpiredPayments(ctx context.Context) ([]db.Payment, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var expiredPayments []db.Payment
	now := time.Now()

	for _, payment := range r.payments {
		if payment.Status == db.PaymentsStatusPENDING &&
			payment.ExpiresAt.Valid &&
			payment.ExpiresAt.Time.Before(now) {
			expiredPayments = append(expiredPayments, *payment)
		}
	}

	return expiredPayments, nil
}

// MockPaymentCallbackRepository 模拟支付回调仓储
type MockPaymentCallbackRepository struct {
	callbacks map[string]*db.PaymentCallback
	mutex     sync.RWMutex
}

// NewMockPaymentCallbackRepository 创建模拟支付回调仓储
func NewMockPaymentCallbackRepository() PaymentCallbackRepository {
	return &MockPaymentCallbackRepository{
		callbacks: make(map[string]*db.PaymentCallback),
	}
}

func (r *MockPaymentCallbackRepository) Create(ctx context.Context, req *model.PaymentCallbackRequest) (*db.PaymentCallback, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	id := uuid.New().String()
	callback := &db.PaymentCallback{
		ID:           id,
		PaymentID:    req.PaymentID,
		CallbackType: db.PaymentCallbacksCallbackType(req.CallbackType),
		RawData:      req.RawData,
		Processed:    false,
		CreatedAt:    time.Now(),
	}

	r.callbacks[callback.ID] = callback
	return callback, nil
}

func (r *MockPaymentCallbackRepository) GetByPaymentID(ctx context.Context, paymentID string) ([]db.PaymentCallback, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var callbacks []db.PaymentCallback
	for _, callback := range r.callbacks {
		if callback.PaymentID == paymentID {
			callbacks = append(callbacks, *callback)
		}
	}

	return callbacks, nil
}

func (r *MockPaymentCallbackRepository) MarkProcessed(ctx context.Context, id string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	callback, exists := r.callbacks[id]
	if !exists {
		return fmt.Errorf("callback not found")
	}

	callback.Processed = true
	now := time.Now()
	callback.ProcessedAt = sql.NullTime{Time: now, Valid: true}

	return nil
}

func (r *MockPaymentCallbackRepository) GetUnprocessed(ctx context.Context) ([]db.PaymentCallback, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var unprocessed []db.PaymentCallback
	for _, callback := range r.callbacks {
		if !callback.Processed {
			unprocessed = append(unprocessed, *callback)
		}
	}

	return unprocessed, nil
}
