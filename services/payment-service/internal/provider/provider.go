package provider

import (
	"context"
	"pay-mall/services/payment-service/internal/model"
)

// PaymentProvider 支付提供商接口
type PaymentProvider interface {
	// CreatePayment 创建支付订单
	CreatePayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error)
	
	// VerifyCallback 验证支付回调
	VerifyCallback(ctx context.Context, rawData string) (*CallbackData, error)
	
	// QueryPayment 查询支付状态
	QueryPayment(ctx context.Context, externalPaymentID string) (*PaymentQueryResponse, error)
	
	// GetProviderName 获取提供商名称
	GetProviderName() string
}

// PaymentRequest 支付请求
type PaymentRequest struct {
	PaymentID     string  `json:"payment_id"`
	OrderID       string  `json:"order_id"`
	Amount        float64 `json:"amount"`
	Currency      string  `json:"currency"`
	Subject       string  `json:"subject"`
	Description   string  `json:"description"`
	NotifyURL     string  `json:"notify_url"`
	ReturnURL     string  `json:"return_url"`
	ExpiresIn     int     `json:"expires_in"` // 过期时间（秒）
}

// PaymentResponse 支付响应
type PaymentResponse struct {
	ExternalPaymentID string      `json:"external_payment_id"`
	PaymentURL        string      `json:"payment_url,omitempty"`
	QRCode            string      `json:"qr_code,omitempty"`
	PaymentData       interface{} `json:"payment_data,omitempty"`
}

// CallbackData 回调数据
type CallbackData struct {
	ExternalPaymentID string                 `json:"external_payment_id"`
	Status            model.PaymentStatus    `json:"status"`
	Amount            float64                `json:"amount"`
	Currency          string                 `json:"currency"`
	PaidAt            *string                `json:"paid_at,omitempty"`
	FailureReason     string                 `json:"failure_reason,omitempty"`
	RawData           map[string]interface{} `json:"raw_data"`
}

// PaymentQueryResponse 支付查询响应
type PaymentQueryResponse struct {
	ExternalPaymentID string              `json:"external_payment_id"`
	Status            model.PaymentStatus `json:"status"`
	Amount            float64             `json:"amount"`
	Currency          string              `json:"currency"`
	PaidAt            *string             `json:"paid_at,omitempty"`
	FailureReason     string              `json:"failure_reason,omitempty"`
}

// ProviderManager 支付提供商管理器
type ProviderManager struct {
	providers map[model.PaymentMethod]PaymentProvider
}

// NewProviderManager 创建支付提供商管理器
func NewProviderManager() *ProviderManager {
	return &ProviderManager{
		providers: make(map[model.PaymentMethod]PaymentProvider),
	}
}

// RegisterProvider 注册支付提供商
func (pm *ProviderManager) RegisterProvider(method model.PaymentMethod, provider PaymentProvider) {
	pm.providers[method] = provider
}

// GetProvider 获取支付提供商
func (pm *ProviderManager) GetProvider(method model.PaymentMethod) (PaymentProvider, bool) {
	provider, exists := pm.providers[method]
	return provider, exists
}
