package provider

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"pay-mall/services/payment-service/internal/config"
	"pay-mall/services/payment-service/internal/model"
	"strconv"
	"time"
)

// AlipayProvider 支付宝支付提供商
type AlipayProvider struct {
	config config.AlipayConfig
}

// NewAlipayProvider 创建支付宝支付提供商
func NewAlipayProvider(cfg config.AlipayConfig) *AlipayProvider {
	return &AlipayProvider{
		config: cfg,
	}
}

// CreatePayment 创建支付宝支付订单
func (p *AlipayProvider) CreatePayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// 模拟支付宝支付订单创建
	// 在实际项目中，这里会调用支付宝的 API
	
	log.Printf("Creating Alipay payment for order %s, amount: %.2f", req.OrderID, req.Amount)
	
	// 生成外部支付ID（模拟）
	externalPaymentID := fmt.Sprintf("alipay_%s_%d", req.PaymentID, time.Now().Unix())
	
	// 构造支付URL（模拟）
	paymentURL := fmt.Sprintf("%s?app_id=%s&out_trade_no=%s&total_amount=%.2f&subject=%s&notify_url=%s&return_url=%s",
		p.config.GatewayURL,
		p.config.AppID,
		externalPaymentID,
		req.Amount,
		req.Subject,
		req.NotifyURL,
		req.ReturnURL,
	)
	
	// 生成二维码内容（模拟）
	qrCode := fmt.Sprintf("alipay://pay?trade_no=%s&amount=%.2f", externalPaymentID, req.Amount)
	
	return &PaymentResponse{
		ExternalPaymentID: externalPaymentID,
		PaymentURL:        paymentURL,
		QRCode:            qrCode,
		PaymentData: map[string]interface{}{
			"app_id":         p.config.AppID,
			"out_trade_no":   externalPaymentID,
			"total_amount":   fmt.Sprintf("%.2f", req.Amount),
			"subject":        req.Subject,
			"product_code":   "FAST_INSTANT_TRADE_PAY",
			"timeout_express": fmt.Sprintf("%dm", req.ExpiresIn/60),
		},
	}, nil
}

// VerifyCallback 验证支付宝回调
func (p *AlipayProvider) VerifyCallback(ctx context.Context, rawData string) (*CallbackData, error) {
	// 模拟解析支付宝回调数据
	// 在实际项目中，这里会验证支付宝的签名
	
	log.Printf("Verifying Alipay callback: %s", rawData)
	
	// 解析回调数据（模拟）
	var callbackParams map[string]interface{}
	if err := json.Unmarshal([]byte(rawData), &callbackParams); err != nil {
		return nil, fmt.Errorf("failed to parse callback data: %w", err)
	}
	
	// 提取关键信息
	externalPaymentID, _ := callbackParams["out_trade_no"].(string)
	tradeStatus, _ := callbackParams["trade_status"].(string)
	totalAmount, _ := callbackParams["total_amount"].(string)
	gmtPayment, _ := callbackParams["gmt_payment"].(string)
	
	// 转换支付状态
	var status model.PaymentStatus
	switch tradeStatus {
	case "TRADE_SUCCESS", "TRADE_FINISHED":
		status = model.PaymentStatusSuccess
	case "TRADE_CLOSED":
		status = model.PaymentStatusCancelled
	default:
		status = model.PaymentStatusFailed
	}
	
	// 解析金额
	amount, _ := strconv.ParseFloat(totalAmount, 64)
	
	return &CallbackData{
		ExternalPaymentID: externalPaymentID,
		Status:            status,
		Amount:            amount,
		Currency:          "CNY",
		PaidAt:            &gmtPayment,
		RawData:           callbackParams,
	}, nil
}

// QueryPayment 查询支付宝支付状态
func (p *AlipayProvider) QueryPayment(ctx context.Context, externalPaymentID string) (*PaymentQueryResponse, error) {
	// 模拟查询支付宝支付状态
	// 在实际项目中，这里会调用支付宝的查询接口
	
	log.Printf("Querying Alipay payment status for: %s", externalPaymentID)
	
	// 模拟返回支付成功状态
	return &PaymentQueryResponse{
		ExternalPaymentID: externalPaymentID,
		Status:            model.PaymentStatusSuccess,
		Amount:            100.00,
		Currency:          "CNY",
		PaidAt:            func() *string { s := time.Now().Format(time.RFC3339); return &s }(),
	}, nil
}

// GetProviderName 获取提供商名称
func (p *AlipayProvider) GetProviderName() string {
	return "Alipay"
}
