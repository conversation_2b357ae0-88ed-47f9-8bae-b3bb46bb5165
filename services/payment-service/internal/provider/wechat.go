package provider

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"pay-mall/services/payment-service/internal/config"
	"pay-mall/services/payment-service/internal/model"
	"strconv"
	"time"
)

// WechatProvider 微信支付提供商
type WechatProvider struct {
	config config.WechatConfig
}

// NewWechatProvider 创建微信支付提供商
func NewWechatProvider(cfg config.WechatConfig) *WechatProvider {
	return &WechatProvider{
		config: cfg,
	}
}

// CreatePayment 创建微信支付订单
func (p *WechatProvider) CreatePayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// 模拟微信支付订单创建
	// 在实际项目中，这里会调用微信支付的统一下单接口
	
	log.Printf("Creating Wechat payment for order %s, amount: %.2f", req.OrderID, req.Amount)
	
	// 生成外部支付ID（模拟）
	externalPaymentID := fmt.Sprintf("wx_%s_%d", req.PaymentID, time.Now().Unix())
	
	// 构造支付URL（模拟）
	paymentURL := fmt.Sprintf("weixin://wxpay/bizpayurl?pr=%s", externalPaymentID)
	
	// 生成二维码内容（模拟）
	qrCode := fmt.Sprintf("weixin://wxpay/bizpayurl?pr=%s", externalPaymentID)
	
	return &PaymentResponse{
		ExternalPaymentID: externalPaymentID,
		PaymentURL:        paymentURL,
		QRCode:            qrCode,
		PaymentData: map[string]interface{}{
			"appid":            p.config.AppID,
			"mch_id":           p.config.MchID,
			"out_trade_no":     externalPaymentID,
			"total_fee":        int(req.Amount * 100), // 微信支付金额单位为分
			"body":             req.Subject,
			"trade_type":       "NATIVE",
			"notify_url":       req.NotifyURL,
			"time_expire":      time.Now().Add(time.Duration(req.ExpiresIn) * time.Second).Format("20060102150405"),
		},
	}, nil
}

// VerifyCallback 验证微信支付回调
func (p *WechatProvider) VerifyCallback(ctx context.Context, rawData string) (*CallbackData, error) {
	// 模拟解析微信支付回调数据
	// 在实际项目中，这里会验证微信支付的签名
	
	log.Printf("Verifying Wechat callback: %s", rawData)
	
	// 解析回调数据（模拟）
	var callbackParams map[string]interface{}
	if err := json.Unmarshal([]byte(rawData), &callbackParams); err != nil {
		return nil, fmt.Errorf("failed to parse callback data: %w", err)
	}
	
	// 提取关键信息
	externalPaymentID, _ := callbackParams["out_trade_no"].(string)
	resultCode, _ := callbackParams["result_code"].(string)
	totalFeeStr, _ := callbackParams["total_fee"].(string)
	timeEnd, _ := callbackParams["time_end"].(string)
	
	// 转换支付状态
	var status model.PaymentStatus
	switch resultCode {
	case "SUCCESS":
		status = model.PaymentStatusSuccess
	case "FAIL":
		status = model.PaymentStatusFailed
	default:
		status = model.PaymentStatusFailed
	}
	
	// 解析金额（微信支付金额单位为分）
	totalFee, _ := strconv.Atoi(totalFeeStr)
	amount := float64(totalFee) / 100
	
	return &CallbackData{
		ExternalPaymentID: externalPaymentID,
		Status:            status,
		Amount:            amount,
		Currency:          "CNY",
		PaidAt:            &timeEnd,
		RawData:           callbackParams,
	}, nil
}

// QueryPayment 查询微信支付状态
func (p *WechatProvider) QueryPayment(ctx context.Context, externalPaymentID string) (*PaymentQueryResponse, error) {
	// 模拟查询微信支付状态
	// 在实际项目中，这里会调用微信支付的查询接口
	
	log.Printf("Querying Wechat payment status for: %s", externalPaymentID)
	
	// 模拟返回支付成功状态
	return &PaymentQueryResponse{
		ExternalPaymentID: externalPaymentID,
		Status:            model.PaymentStatusSuccess,
		Amount:            100.00,
		Currency:          "CNY",
		PaidAt:            func() *string { s := time.Now().Format("20060102150405"); return &s }(),
	}, nil
}

// GetProviderName 获取提供商名称
func (p *WechatProvider) GetProviderName() string {
	return "Wechat"
}
