package provider

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"pay-mall/services/payment-service/internal/config"
	"pay-mall/services/payment-service/internal/model"
	"strconv"
	"time"
)

// BankCardProvider 银行卡支付提供商
type BankCardProvider struct {
	config config.BankCardConfig
}

// NewBankCardProvider 创建银行卡支付提供商
func NewBankCardProvider(cfg config.BankCardConfig) *BankCardProvider {
	return &BankCardProvider{
		config: cfg,
	}
}

// CreatePayment 创建银行卡支付订单
func (p *BankCardProvider) CreatePayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// 模拟银行卡支付订单创建
	// 在实际项目中，这里会调用银行或第三方支付网关的接口
	
	log.Printf("Creating BankCard payment for order %s, amount: %.2f", req.OrderID, req.Amount)
	
	// 生成外部支付ID（模拟）
	externalPaymentID := fmt.Sprintf("bank_%s_%d", req.PaymentID, time.Now().Unix())
	
	// 构造支付URL（模拟）
	paymentURL := fmt.Sprintf("%s/pay?merchant_id=%s&order_no=%s&amount=%.2f&notify_url=%s&return_url=%s",
		p.config.GatewayURL,
		p.config.MerchantID,
		externalPaymentID,
		req.Amount,
		req.NotifyURL,
		req.ReturnURL,
	)
	
	return &PaymentResponse{
		ExternalPaymentID: externalPaymentID,
		PaymentURL:        paymentURL,
		PaymentData: map[string]interface{}{
			"merchant_id":    p.config.MerchantID,
			"order_no":       externalPaymentID,
			"amount":         fmt.Sprintf("%.2f", req.Amount),
			"currency":       req.Currency,
			"subject":        req.Subject,
			"description":    req.Description,
			"notify_url":     req.NotifyURL,
			"return_url":     req.ReturnURL,
			"expire_time":    time.Now().Add(time.Duration(req.ExpiresIn) * time.Second).Format(time.RFC3339),
		},
	}, nil
}

// VerifyCallback 验证银行卡支付回调
func (p *BankCardProvider) VerifyCallback(ctx context.Context, rawData string) (*CallbackData, error) {
	// 模拟解析银行卡支付回调数据
	// 在实际项目中，这里会验证银行或支付网关的签名
	
	log.Printf("Verifying BankCard callback: %s", rawData)
	
	// 解析回调数据（模拟）
	var callbackParams map[string]interface{}
	if err := json.Unmarshal([]byte(rawData), &callbackParams); err != nil {
		return nil, fmt.Errorf("failed to parse callback data: %w", err)
	}
	
	// 提取关键信息
	externalPaymentID, _ := callbackParams["order_no"].(string)
	status, _ := callbackParams["status"].(string)
	amountStr, _ := callbackParams["amount"].(string)
	payTime, _ := callbackParams["pay_time"].(string)
	
	// 转换支付状态
	var paymentStatus model.PaymentStatus
	switch status {
	case "SUCCESS", "PAID":
		paymentStatus = model.PaymentStatusSuccess
	case "FAILED", "ERROR":
		paymentStatus = model.PaymentStatusFailed
	case "CANCELLED":
		paymentStatus = model.PaymentStatusCancelled
	default:
		paymentStatus = model.PaymentStatusFailed
	}
	
	// 解析金额
	amount, _ := strconv.ParseFloat(amountStr, 64)
	
	return &CallbackData{
		ExternalPaymentID: externalPaymentID,
		Status:            paymentStatus,
		Amount:            amount,
		Currency:          "CNY",
		PaidAt:            &payTime,
		RawData:           callbackParams,
	}, nil
}

// QueryPayment 查询银行卡支付状态
func (p *BankCardProvider) QueryPayment(ctx context.Context, externalPaymentID string) (*PaymentQueryResponse, error) {
	// 模拟查询银行卡支付状态
	// 在实际项目中，这里会调用银行或支付网关的查询接口
	
	log.Printf("Querying BankCard payment status for: %s", externalPaymentID)
	
	// 模拟返回支付成功状态
	return &PaymentQueryResponse{
		ExternalPaymentID: externalPaymentID,
		Status:            model.PaymentStatusSuccess,
		Amount:            100.00,
		Currency:          "CNY",
		PaidAt:            func() *string { s := time.Now().Format(time.RFC3339); return &s }(),
	}, nil
}

// GetProviderName 获取提供商名称
func (p *BankCardProvider) GetProviderName() string {
	return "BankCard"
}
