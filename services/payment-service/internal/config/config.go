package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v2"
)

// Config 支付服务配置
type Config struct {
	App      AppConfig      `yaml:"app"`
	Database DatabaseConfig `yaml:"database"`
	Redis    RedisConfig    `yaml:"redis"`
	Kafka    KafkaConfig    `yaml:"kafka"`
	JWT      JWTConfig      `yaml:"jwt"`
	Payment  PaymentConfig  `yaml:"payment"`
}

// AppConfig 应用配置
type AppConfig struct {
	Name string `yaml:"name"`
	Port int    `yaml:"port"`
	Env  string `yaml:"env"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	User            string `yaml:"user"`
	Password        string `yaml:"password"`
	DBName          string `yaml:"dbname"`
	MaxOpenConns    int    `yaml:"max_open_conns"`
	MaxIdleConns    int    `yaml:"max_idle_conns"`
	ConnMaxLifetime int    `yaml:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `yaml:"host"`
	Port         int    `yaml:"port"`
	Password     string `yaml:"password"`
	DB           int    `yaml:"db"`
	PoolSize     int    `yaml:"pool_size"`
	MinIdleConns int    `yaml:"min_idle_conns"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers          []string `yaml:"brokers"`
	ConsumerGroup    string   `yaml:"consumer_group"`
	AutoOffsetReset  string   `yaml:"auto_offset_reset"`
	EnableAutoCommit bool     `yaml:"enable_auto_commit"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret    string `yaml:"secret"`
	ExpiresIn int    `yaml:"expires_in"`
}

// PaymentConfig 支付相关配置
type PaymentConfig struct {
	DefaultExpiresIn int            `yaml:"default_expires_in"` // 默认过期时间（秒）
	Alipay           AlipayConfig   `yaml:"alipay"`
	Wechat           WechatConfig   `yaml:"wechat"`
	BankCard         BankCardConfig `yaml:"bank_card"`
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID      string `yaml:"app_id"`
	PrivateKey string `yaml:"private_key"`
	PublicKey  string `yaml:"public_key"`
	GatewayURL string `yaml:"gateway_url"`
	NotifyURL  string `yaml:"notify_url"`
	ReturnURL  string `yaml:"return_url"`
}

// WechatConfig 微信支付配置
type WechatConfig struct {
	AppID     string `yaml:"app_id"`
	MchID     string `yaml:"mch_id"`
	APIKey    string `yaml:"api_key"`
	CertPath  string `yaml:"cert_path"`
	KeyPath   string `yaml:"key_path"`
	NotifyURL string `yaml:"notify_url"`
	ReturnURL string `yaml:"return_url"`
}

// BankCardConfig 银行卡支付配置
type BankCardConfig struct {
	MerchantID string `yaml:"merchant_id"`
	SecretKey  string `yaml:"secret_key"`
	GatewayURL string `yaml:"gateway_url"`
	NotifyURL  string `yaml:"notify_url"`
	ReturnURL  string `yaml:"return_url"`
}

// LoadConfig 加载配置
func LoadConfig(configPath string) (*Config, error) {
	var cfg Config

	// 确定配置文件路径
	if configPath == "" {
		env := os.Getenv("APP_ENV")
		if env == "" {
			env = "config" // 默认配置文件
		}
		configPath = fmt.Sprintf("configs/%s.yaml", env)
	}

	// 读取文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	// 解析 YAML
	err = yaml.Unmarshal(data, &cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config file %s: %w", configPath, err)
	}

	return &cfg, nil
}
