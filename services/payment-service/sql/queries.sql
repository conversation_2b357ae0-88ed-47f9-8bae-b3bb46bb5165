-- 支付相关查询

-- name: CreatePayment :exec
INSERT INTO payments (
    id, order_id, user_id, amount, currency, payment_method, 
    status, callback_url, return_url, description, metadata, expires_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: GetPaymentByID :one
SELECT * FROM payments WHERE id = ?;

-- name: GetPaymentByOrderID :one
SELECT * FROM payments WHERE order_id = ? ORDER BY created_at DESC LIMIT 1;

-- name: GetPaymentsByUserID :many
SELECT * FROM payments WHERE user_id = ? ORDER BY created_at DESC;

-- name: UpdatePaymentStatus :exec
UPDATE payments 
SET status = ?, external_payment_id = ?, paid_at = ?, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?;

-- name: UpdatePaymentExternalID :exec
UPDATE payments 
SET external_payment_id = ?, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?;

-- name: GetExpiredPayments :many
SELECT * FROM payments 
WHERE status = 'PENDING' AND expires_at < NOW();

-- name: CreatePaymentCallback :exec
INSERT INTO payment_callbacks (
    id, payment_id, callback_type, raw_data
) VALUES (?, ?, ?, ?);

-- name: GetPaymentCallbacks :many
SELECT * FROM payment_callbacks 
WHERE payment_id = ? ORDER BY created_at DESC;

-- name: MarkCallbackProcessed :exec
UPDATE payment_callbacks 
SET processed = TRUE, processed_at = CURRENT_TIMESTAMP 
WHERE id = ?;

-- name: GetUnprocessedCallbacks :many
SELECT * FROM payment_callbacks 
WHERE processed = FALSE ORDER BY created_at ASC;
