-- 支付服务数据库表结构

-- 支付记录表
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY COMMENT '支付ID',
    order_id VARCHAR(36) NOT NULL COMMENT '订单ID',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    currency VARCHAR(3) NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
    payment_method ENUM('ALIPAY', 'WECHAT', 'BANK_CARD') NOT NULL COMMENT '支付方式',
    status ENUM('PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED') NOT NULL DEFAULT 'PENDING' COMMENT '支付状态',
    external_payment_id VARCHAR(255) NULL COMMENT '第三方支付平台的支付ID',
    callback_url VARCHAR(500) NULL COMMENT '支付回调URL',
    return_url VARCHAR(500) NULL COMMENT '支付成功后跳转URL',
    description TEXT NULL COMMENT '支付描述',
    metadata JSON NULL COMMENT '额外的元数据',
    expires_at TIMESTAMP NULL COMMENT '支付过期时间',
    paid_at TIMESTAMP NULL COMMENT '支付完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_method (payment_method),
    INDEX idx_external_payment_id (external_payment_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- 支付回调记录表
CREATE TABLE payment_callbacks (
    id VARCHAR(36) PRIMARY KEY COMMENT '回调记录ID',
    payment_id VARCHAR(36) NOT NULL COMMENT '关联支付ID',
    callback_type ENUM('NOTIFY', 'RETURN') NOT NULL COMMENT '回调类型',
    raw_data TEXT NOT NULL COMMENT '原始回调数据',
    processed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已处理',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_payment_id (payment_id),
    INDEX idx_callback_type (callback_type),
    INDEX idx_processed (processed),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付回调记录表';
