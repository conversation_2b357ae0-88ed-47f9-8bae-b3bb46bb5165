# Product Service Makefile

.PHONY: help build test test-unit test-integration test-coverage clean deps mock docker-up docker-down

# 默认目标
.DEFAULT_GOAL := help

# 变量定义
APP_NAME := product-service
GO_VERSION := 1.21
DOCKER_COMPOSE_FILE := docker-compose.test.yml

# 颜色定义
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m

# 帮助信息
help: ## 显示帮助信息
	@echo "$(BLUE)Product Service 构建和测试命令$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# 依赖管理
deps: ## 安装依赖
	@echo "$(BLUE)安装 Go 依赖...$(NC)"
	go mod download
	go mod tidy

# 生成 Mock 对象
mock: ## 生成 Mock 对象
	@echo "$(BLUE)生成 Mock 对象...$(NC)"
	@if ! command -v mockgen > /dev/null; then \
		echo "$(YELLOW)安装 mockgen...$(NC)"; \
		go install go.uber.org/mock/mockgen@latest; \
	fi
	@mkdir -p test/mocks
	mockgen -source=internal/repository/category_repository.go -destination=test/mocks/mock_category_repository.go -package=mocks
	mockgen -source=internal/repository/product_repository.go -destination=test/mocks/mock_product_repository.go -package=mocks
	mockgen -source=internal/repository/product_variant_repository.go -destination=test/mocks/mock_product_variant_repository.go -package=mocks
	mockgen -source=internal/service/category_service.go -destination=test/mocks/mock_category_service.go -package=mocks
	mockgen -source=internal/service/product_service.go -destination=test/mocks/mock_product_service.go -package=mocks
	mockgen -source=internal/service/event_service.go -destination=test/mocks/mock_event_service.go -package=mocks
	@echo "$(GREEN)Mock 对象生成完成$(NC)"

# 构建
build: deps ## 构建应用
	@echo "$(BLUE)构建应用...$(NC)"
	go build -o bin/$(APP_NAME) cmd/main.go
	@echo "$(GREEN)构建完成: bin/$(APP_NAME)$(NC)"

# 代码检查
lint: ## 运行代码检查
	@echo "$(BLUE)运行代码检查...$(NC)"
	@if ! command -v golangci-lint > /dev/null; then \
		echo "$(YELLOW)安装 golangci-lint...$(NC)"; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
	fi
	golangci-lint run

# 格式化代码
fmt: ## 格式化代码
	@echo "$(BLUE)格式化代码...$(NC)"
	go fmt ./...
	@if command -v goimports > /dev/null; then \
		goimports -w .; \
	fi

# 单元测试
test-unit: mock ## 运行单元测试
	@echo "$(BLUE)运行单元测试...$(NC)"
	go test -v ./test/unit/... -short -race
	@echo "$(GREEN)单元测试完成$(NC)"

# 集成测试
test-integration: docker-up ## 运行集成测试
	@echo "$(BLUE)运行集成测试...$(NC)"
	@export TEST_DB_HOST=localhost && \
	export TEST_DB_PORT=3307 && \
	export TEST_DB_USER=testuser && \
	export TEST_DB_PASSWORD=testpassword && \
	export TEST_DB_NAME=product_service_test && \
	export TEST_KAFKA_BROKER=localhost:9092 && \
	go test -v ./test/integration/... -timeout=5m
	@$(MAKE) docker-down
	@echo "$(GREEN)集成测试完成$(NC)"

# 所有测试
test: test-unit test-integration ## 运行所有测试

# 测试覆盖率
test-coverage: mock docker-up ## 生成测试覆盖率报告
	@echo "$(BLUE)生成测试覆盖率报告...$(NC)"
	@mkdir -p coverage
	@# 单元测试覆盖率
	go test -v ./test/unit/... -short -coverprofile=coverage/unit.out -covermode=atomic
	@# 集成测试覆盖率
	@export TEST_DB_HOST=localhost && \
	export TEST_DB_PORT=3307 && \
	export TEST_DB_USER=testuser && \
	export TEST_DB_PASSWORD=testpassword && \
	export TEST_DB_NAME=product_service_test && \
	export TEST_KAFKA_BROKER=localhost:9092 && \
	go test -v ./test/integration/... -coverprofile=coverage/integration.out -covermode=atomic -timeout=5m
	@# 合并覆盖率文件
	@echo "mode: atomic" > coverage/total.out
	@tail -n +2 coverage/unit.out >> coverage/total.out 2>/dev/null || true
	@tail -n +2 coverage/integration.out >> coverage/total.out 2>/dev/null || true
	@# 生成 HTML 报告
	go tool cover -html=coverage/total.out -o coverage/coverage.html
	@# 显示覆盖率统计
	@echo "$(BLUE)覆盖率统计:$(NC)"
	go tool cover -func=coverage/total.out
	@$(MAKE) docker-down
	@echo "$(GREEN)覆盖率报告已生成: coverage/coverage.html$(NC)"

# Docker 环境管理
docker-up: ## 启动测试环境
	@echo "$(BLUE)启动测试环境...$(NC)"
	@if ! docker info > /dev/null 2>&1; then \
		echo "$(RED)错误: Docker 未运行$(NC)"; \
		exit 1; \
	fi
	docker-compose -f $(DOCKER_COMPOSE_FILE) down -v > /dev/null 2>&1 || true
	docker-compose -f $(DOCKER_COMPOSE_FILE) up -d
	@echo "$(YELLOW)等待服务启动...$(NC)"
	@# 等待 MySQL
	@timeout=60; \
	while [ $$timeout -gt 0 ]; do \
		if docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T mysql mysqladmin ping -h localhost --silent 2>/dev/null; then \
			break; \
		fi; \
		sleep 2; \
		timeout=$$((timeout-2)); \
	done; \
	if [ $$timeout -le 0 ]; then \
		echo "$(RED)MySQL 启动超时$(NC)"; \
		exit 1; \
	fi
	@# 等待 Kafka
	@timeout=60; \
	while [ $$timeout -gt 0 ]; do \
		if docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T kafka kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1; then \
			break; \
		fi; \
		sleep 2; \
		timeout=$$((timeout-2)); \
	done; \
	if [ $$timeout -le 0 ]; then \
		echo "$(RED)Kafka 启动超时$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)测试环境启动完成$(NC)"

docker-down: ## 停止测试环境
	@echo "$(BLUE)停止测试环境...$(NC)"
	docker-compose -f $(DOCKER_COMPOSE_FILE) down -v
	@echo "$(GREEN)测试环境已停止$(NC)"

# 清理
clean: docker-down ## 清理构建文件和测试环境
	@echo "$(BLUE)清理构建文件...$(NC)"
	rm -rf bin/
	rm -rf coverage/
	rm -rf test/mocks/
	docker system prune -f > /dev/null 2>&1 || true
	@echo "$(GREEN)清理完成$(NC)"

# 快速测试（仅单元测试）
quick-test: ## 快速测试（仅单元测试）
	@echo "$(BLUE)运行快速测试...$(NC)"
	@$(MAKE) test-unit
	@echo "$(GREEN)快速测试完成$(NC)"

# 开发环境设置
dev-setup: deps mock ## 设置开发环境
	@echo "$(BLUE)设置开发环境...$(NC)"
	@if ! command -v golangci-lint > /dev/null; then \
		echo "$(YELLOW)安装 golangci-lint...$(NC)"; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
	fi
	@if ! command -v goimports > /dev/null; then \
		echo "$(YELLOW)安装 goimports...$(NC)"; \
		go install golang.org/x/tools/cmd/goimports@latest; \
	fi
	@echo "$(GREEN)开发环境设置完成$(NC)"

# CI/CD 流水线
ci: deps lint test-coverage ## CI/CD 流水线
	@echo "$(GREEN)CI/CD 流水线完成$(NC)"
