-- name: CreateProductVariant :exec
INSERT INTO product_variants (
    id, product_id, name, sku, price, original_price, stock_quantity, 
    reserved_quantity, image_url, attributes, weight, dimensions, 
    status, sort_order, created_at, updated_at
) VALUES (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
);

-- name: GetProductVariantByID :one
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE id = ? AND status != 'INACTIVE';

-- name: GetProductVariantBySKU :one
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE sku = ? AND status != 'INACTIVE';

-- name: ListProductVariants :many
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE product_id = ? AND status != 'INACTIVE'
ORDER BY sort_order ASC, created_at ASC;

-- name: ListAllProductVariants :many
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE product_id = ?
ORDER BY sort_order ASC, created_at ASC;

-- name: CountProductVariants :one
SELECT COUNT(*) FROM product_variants WHERE product_id = ? AND status != 'INACTIVE';

-- name: UpdateProductVariant :exec
UPDATE product_variants 
SET name = ?, price = ?, original_price = ?, stock_quantity = ?, 
    image_url = ?, attributes = ?, weight = ?, dimensions = ?, 
    sort_order = ?, updated_at = ?
WHERE id = ?;

-- name: UpdateProductVariantStatus :exec
UPDATE product_variants 
SET status = ?, updated_at = ?
WHERE id = ?;

-- name: UpdateProductVariantStock :exec
UPDATE product_variants 
SET stock_quantity = ?, updated_at = ?
WHERE id = ?;

-- name: ReserveProductVariantStock :exec
UPDATE product_variants 
SET reserved_quantity = reserved_quantity + ?, updated_at = ?
WHERE id = ? AND (stock_quantity - reserved_quantity) >= ?;

-- name: ReleaseProductVariantStock :exec
UPDATE product_variants 
SET reserved_quantity = GREATEST(0, reserved_quantity - ?), updated_at = ?
WHERE id = ?;

-- name: ConsumeProductVariantStock :exec
UPDATE product_variants 
SET stock_quantity = stock_quantity - ?, 
    reserved_quantity = GREATEST(0, reserved_quantity - ?), 
    updated_at = ?
WHERE id = ?;

-- name: DeleteProductVariant :exec
UPDATE product_variants 
SET status = 'INACTIVE', updated_at = ?
WHERE id = ?;

-- name: HardDeleteProductVariant :exec
DELETE FROM product_variants WHERE id = ?;

-- name: DeleteProductVariantsByProductID :exec
UPDATE product_variants 
SET status = 'INACTIVE', updated_at = ?
WHERE product_id = ?;

-- name: GetAvailableProductVariants :many
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE product_id = ? AND status = 'ACTIVE' AND (stock_quantity - reserved_quantity) > 0
ORDER BY sort_order ASC, created_at ASC;
