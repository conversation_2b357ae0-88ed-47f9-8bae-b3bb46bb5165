-- name: CreateProduct :exec
INSERT INTO products (
    id, name, description, price, original_price, image_url, image_urls, 
    category_id, brand, sku, weight, dimensions, tags, attributes, 
    status, sort_order, view_count, sale_count, created_at, updated_at
) VALUES (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
);

-- name: GetProductByID :one
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE id = ? AND status != 'DELETED';

-- name: GetProductBySKU :one
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE sku = ? AND status != 'DELETED';

-- name: ListProducts :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE status != 'DELETED'
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?;

-- name: ListProductsByCategory :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE category_id = ? AND status != 'DELETED'
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?;

-- name: ListProductsByStatus :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE status = ?
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?;

-- name: CountProducts :one
SELECT COUNT(*) FROM products WHERE status != 'DELETED';

-- name: CountProductsByCategory :one
SELECT COUNT(*) FROM products WHERE category_id = ? AND status != 'DELETED';

-- name: CountProductsByStatus :one
SELECT COUNT(*) FROM products WHERE status = ?;

-- name: SearchProducts :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE (name LIKE ? OR description LIKE ? OR brand LIKE ?) 
  AND status != 'DELETED'
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?;

-- name: CountSearchProducts :one
SELECT COUNT(*) 
FROM products 
WHERE (name LIKE ? OR description LIKE ? OR brand LIKE ?) 
  AND status != 'DELETED';

-- name: UpdateProduct :exec
UPDATE products 
SET name = ?, description = ?, price = ?, original_price = ?, 
    image_url = ?, image_urls = ?, category_id = ?, brand = ?, 
    weight = ?, dimensions = ?, tags = ?, attributes = ?, 
    sort_order = ?, updated_at = ?
WHERE id = ?;

-- name: UpdateProductStatus :exec
UPDATE products 
SET status = ?, updated_at = ?
WHERE id = ?;

-- name: IncrementViewCount :exec
UPDATE products 
SET view_count = view_count + 1, updated_at = ?
WHERE id = ?;

-- name: IncrementSaleCount :exec
UPDATE products 
SET sale_count = sale_count + ?, updated_at = ?
WHERE id = ?;

-- name: DeleteProduct :exec
UPDATE products 
SET status = 'DELETED', updated_at = ?
WHERE id = ?;

-- name: HardDeleteProduct :exec
DELETE FROM products WHERE id = ?;

-- name: GetPopularProducts :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE status = 'ACTIVE'
ORDER BY sale_count DESC, view_count DESC
LIMIT ?;

-- name: GetProductsByCategory :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE category_id = ? AND status = 'ACTIVE'
ORDER BY sort_order ASC, created_at DESC
LIMIT ?;
