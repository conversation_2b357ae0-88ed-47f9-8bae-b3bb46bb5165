-- name: CreateCategory :exec
INSERT INTO categories (
    id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
) VALUES (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
);

-- name: GetCategoryByID :one
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE id = ? AND is_active = TRUE;

-- name: GetCategoryByIDWithInactive :one
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE id = ?;

-- name: ListCategories :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE is_active = TRUE
ORDER BY level ASC, sort_order ASC, created_at ASC
LIMIT ? OFFSET ?;

-- name: CountCategories :one
SELECT COUNT(*) FROM categories WHERE is_active = TRUE;

-- name: GetCategoryChildren :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE parent_id = ? AND is_active = TRUE
ORDER BY sort_order ASC, created_at ASC;

-- name: GetCategoryTree :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE is_active = TRUE
ORDER BY level ASC, sort_order ASC, created_at ASC;

-- name: GetCategoryPath :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories
WHERE id = ?;

-- name: UpdateCategory :exec
UPDATE categories 
SET name = ?, description = ?, sort_order = ?, is_active = ?, updated_at = ?
WHERE id = ?;

-- name: DeleteCategory :exec
UPDATE categories 
SET is_active = FALSE, updated_at = ?
WHERE id = ?;

-- name: HardDeleteCategory :exec
DELETE FROM categories WHERE id = ?;

-- name: GetRootCategories :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE parent_id IS NULL AND is_active = TRUE
ORDER BY sort_order ASC, created_at ASC;
