-- 创建商品表
CREATE TABLE products (
    id VARCHAR(36) PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    image_url VARCHAR(500),
    image_urls JSON,
    category_id VARCHAR(36) NOT NULL,
    brand VARCHAR(100),
    sku VARCHAR(100) UNIQUE,
    weight DECIMAL(8,3),
    dimensions JSON,
    tags JSON,
    attributes JSON,
    status ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK', 'DISCONTINUED', 'DELETED') NOT NULL DEFAULT 'DRAFT',
    sort_order INT NOT NULL DEFAULT 0,
    view_count INT NOT NULL DEFAULT 0,
    sale_count INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_id (category_id),
    INDEX idx_brand (brand),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_view_count (view_count),
    INDEX idx_sale_count (sale_count),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_name_description (name, description),
    
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
