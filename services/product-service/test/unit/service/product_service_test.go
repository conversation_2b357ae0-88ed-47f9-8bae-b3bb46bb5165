package service

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/service"
	"pay-mall/services/product-service/test/mocks"
	"pay-mall/services/product-service/test/testutil"
)

func TestProductService_Create(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockProductRepo := mocks.NewMockProductRepository(ctrl)
	mockProductVariantRepo := mocks.NewMockProductVariantRepository(ctrl)
	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	productService := service.NewProductService(
		mockProductRepo,
		mockProductVariantRepo,
		mockCategoryRepo,
		mockEventService,
	)

	tests := []struct {
		name    string
		req     *model.ProductCreateRequest
		setup   func()
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功创建商品",
			req: &model.ProductCreateRequest{
				Name:        "测试商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("TEST-SKU-001"),
				Price:       99.99,
				SortOrder:   testutil.IntPtr(1),
			},
			setup: func() {
				// 验证分类存在
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(&model.Category{
						ID:       uuid.New().String(),
						Name:     "测试分类",
						IsActive: true,
					}, nil).
					Times(1)

				// 验证SKU不重复
				mockProductRepo.EXPECT().
					GetBySKU(gomock.Any(), "TEST-SKU-001").
					Return(nil, sql.ErrNoRows).
					Times(1)

				mockProductRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)

				mockEventService.EXPECT().
					PublishProductCreated(gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "分类不存在应该失败",
			req: &model.ProductCreateRequest{
				Name:        "测试商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  "non-existent-category",
				SKU:         testutil.StringPtr("TEST-SKU-002"),
				Price:       99.99,
			},
			setup: func() {
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), "non-existent-category").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "category not found",
		},
		{
			name: "SKU重复应该失败",
			req: &model.ProductCreateRequest{
				Name:        "测试商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("DUPLICATE-SKU"),
				Price:       99.99,
			},
			setup: func() {
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(&model.Category{
						ID:       uuid.New().String(),
						Name:     "测试分类",
						IsActive: true,
					}, nil).
					Times(1)

				mockProductRepo.EXPECT().
					GetBySKU(gomock.Any(), "DUPLICATE-SKU").
					Return(&model.Product{
						ID:  uuid.New().String(),
						SKU: testutil.StringPtr("DUPLICATE-SKU"),
					}, nil).
					Times(1)
			},
			wantErr: true,
			errMsg:  "SKU already exists",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			result, err := productService.Create(context.Background(), tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.req.Name, result.Name)
				if tt.req.SKU != nil && result.SKU != nil {
					assert.Equal(t, *tt.req.SKU, *result.SKU)
				}
				assert.Equal(t, tt.req.Price, result.Price)
				assert.NotEmpty(t, result.ID)
				assert.Equal(t, model.ProductStatusDraft, result.Status)
			}
		})
	}
}

func TestProductService_GetByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockProductRepo := mocks.NewMockProductRepository(ctrl)
	mockProductVariantRepo := mocks.NewMockProductVariantRepository(ctrl)
	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	productService := service.NewProductService(
		mockProductRepo,
		mockProductVariantRepo,
		mockCategoryRepo,
		mockEventService,
	)

	tests := []struct {
		name    string
		id      string
		setup   func()
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功获取商品",
			id:   uuid.New().String(),
			setup: func() {
				expectedProduct := &model.Product{
					ID:          uuid.New().String(),
					Name:        "测试商品",
					Description: testutil.StringPtr("测试商品描述"),
					SKU:         testutil.StringPtr("TEST-SKU-001"),
					Price:       99.99,
					Status:      model.ProductStatusActive,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}
				mockProductRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(expectedProduct, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "商品不存在",
			id:   "non-existent-id",
			setup: func() {
				mockProductRepo.EXPECT().
					GetByID(gomock.Any(), "non-existent-id").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "failed to get product",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			result, err := productService.GetByID(context.Background(), tt.id)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotEmpty(t, result.ID)
				assert.NotEmpty(t, result.Name)
				assert.NotEmpty(t, result.SKU)
			}
		})
	}
}

func TestProductService_GetBySKU(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockProductRepo := mocks.NewMockProductRepository(ctrl)
	mockProductVariantRepo := mocks.NewMockProductVariantRepository(ctrl)
	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	productService := service.NewProductService(
		mockProductRepo,
		mockProductVariantRepo,
		mockCategoryRepo,
		mockEventService,
	)

	tests := []struct {
		name    string
		sku     string
		setup   func()
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功通过SKU获取商品",
			sku:  "TEST-SKU-001",
			setup: func() {
				expectedProduct := &model.Product{
					ID:          uuid.New().String(),
					Name:        "测试商品",
					Description: testutil.StringPtr("测试商品描述"),
					SKU:         testutil.StringPtr("TEST-SKU-001"),
					Price:       99.99,
					Status:      model.ProductStatusActive,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}
				mockProductRepo.EXPECT().
					GetBySKU(gomock.Any(), "TEST-SKU-001").
					Return(expectedProduct, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "SKU不存在",
			sku:  "NON-EXISTENT-SKU",
			setup: func() {
				mockProductRepo.EXPECT().
					GetBySKU(gomock.Any(), "NON-EXISTENT-SKU").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "failed to get product by SKU",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			result, err := productService.GetBySKU(context.Background(), tt.sku)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if result.SKU != nil {
					assert.Equal(t, tt.sku, *result.SKU)
				}
			}
		})
	}
}

func TestProductService_IncrementViewCount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockProductRepo := mocks.NewMockProductRepository(ctrl)
	mockProductVariantRepo := mocks.NewMockProductVariantRepository(ctrl)
	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	productService := service.NewProductService(
		mockProductRepo,
		mockProductVariantRepo,
		mockCategoryRepo,
		mockEventService,
	)

	tests := []struct {
		name    string
		id      string
		setup   func()
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功增加浏览次数",
			id:   uuid.New().String(),
			setup: func() {
				mockProductRepo.EXPECT().
					IncrementViewCount(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "商品不存在",
			id:   "non-existent-id",
			setup: func() {
				mockProductRepo.EXPECT().
					IncrementViewCount(gomock.Any(), "non-existent-id").
					Return(sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "failed to increment view count",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			err := productService.IncrementViewCount(context.Background(), tt.id)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestProductService_UpdateStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockProductRepo := mocks.NewMockProductRepository(ctrl)
	mockProductVariantRepo := mocks.NewMockProductVariantRepository(ctrl)
	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	productService := service.NewProductService(
		mockProductRepo,
		mockProductVariantRepo,
		mockCategoryRepo,
		mockEventService,
	)

	tests := []struct {
		name      string
		id        string
		newStatus model.ProductStatus
		setup     func()
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "成功更新商品状态",
			id:        uuid.New().String(),
			newStatus: model.ProductStatusInactive,
			setup: func() {
				existingProduct := &model.Product{
					ID:        uuid.New().String(),
					Name:      "测试商品",
					Status:    model.ProductStatusActive,
					CreatedAt: time.Now().Add(-time.Hour),
					UpdatedAt: time.Now().Add(-time.Hour),
				}

				mockProductRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(existingProduct, nil).
					Times(1)

				mockProductRepo.EXPECT().
					Update(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)

				mockEventService.EXPECT().
					PublishProductStatusChanged(gomock.Any(), model.ProductStatusActive, model.ProductStatusInactive).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name:      "商品不存在",
			id:        "non-existent-id",
			newStatus: model.ProductStatusInactive,
			setup: func() {
				mockProductRepo.EXPECT().
					GetByID(gomock.Any(), "non-existent-id").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "product not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			err := productService.UpdateStatus(context.Background(), tt.id, tt.newStatus)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
