package service

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/repository"
	"pay-mall/services/product-service/internal/service"
	"pay-mall/services/product-service/test/mocks"
	"pay-mall/services/product-service/test/testutil"
)

func TestCategoryService_Create(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	categoryService := service.NewCategoryService(mockCategoryRepo, mockEventService)

	tests := []struct {
		name    string
		req     *model.CategoryCreateRequest
		setup   func()
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功创建分类",
			req: &model.CategoryCreateRequest{
				Name:        "测试分类",
				Description: testutil.StringPtr("测试分类描述"),
				IconURL:     testutil.StringPtr("https://example.com/icon.png"),
				SortOrder:   testutil.IntPtr(1),
			},
			setup: func() {
				mockCategoryRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)

				mockEventService.EXPECT().
					PublishCategoryCreated(gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "创建子分类",
			req: &model.CategoryCreateRequest{
				Name:        "子分类",
				Description: testutil.StringPtr("子分类描述"),
				ParentID:    testutil.StringPtr(uuid.New().String()),
				IconURL:     testutil.StringPtr("https://example.com/icon.png"),
				SortOrder:   testutil.IntPtr(1),
			},
			setup: func() {
				// 验证父分类存在
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(&model.Category{
						ID:       uuid.New().String(),
						Name:     "父分类",
						IsActive: true,
					}, nil).
					Times(1)

				mockCategoryRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)

				mockEventService.EXPECT().
					PublishCategoryCreated(gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "父分类不存在应该失败",
			req: &model.CategoryCreateRequest{
				Name:        "子分类",
				Description: testutil.StringPtr("子分类描述"),
				ParentID:    testutil.StringPtr("non-existent-parent"),
				IconURL:     testutil.StringPtr("https://example.com/icon.png"),
				SortOrder:   testutil.IntPtr(1),
			},
			setup: func() {
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), "non-existent-parent").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "parent category not found",
		},
		{
			name: "数据库错误应该失败",
			req: &model.CategoryCreateRequest{
				Name:        "测试分类",
				Description: testutil.StringPtr("测试分类描述"),
				IconURL:     testutil.StringPtr("https://example.com/icon.png"),
				SortOrder:   testutil.IntPtr(1),
			},
			setup: func() {
				mockCategoryRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(repository.ErrDuplicateKey).
					Times(1)
			},
			wantErr: true,
			errMsg:  "duplicate key",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			result, err := categoryService.Create(context.Background(), tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.req.Name, result.Name)
				assert.Equal(t, tt.req.Description, result.Description)
				assert.NotEmpty(t, result.ID)
				assert.True(t, result.IsActive)
			}
		})
	}
}

func TestCategoryService_GetByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	categoryService := service.NewCategoryService(mockCategoryRepo, mockEventService)

	tests := []struct {
		name    string
		id      string
		setup   func()
		want    *model.Category
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功获取分类",
			id:   uuid.New().String(),
			setup: func() {
				expectedCategory := &model.Category{
					ID:          uuid.New().String(),
					Name:        "测试分类",
					Description: testutil.StringPtr("测试分类描述"),
					IsActive:    true,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(expectedCategory, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "分类不存在",
			id:   "non-existent-id",
			setup: func() {
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), "non-existent-id").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "failed to get category",
		},
		{
			name: "无效ID格式",
			id:   "",
			setup: func() {
				// 不需要调用 repository，在 service 层就会验证失败
			},
			wantErr: true,
			errMsg:  "invalid input",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			result, err := categoryService.GetByID(context.Background(), tt.id)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotEmpty(t, result.ID)
				assert.NotEmpty(t, result.Name)
			}
		})
	}
}

func TestCategoryService_Update(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	categoryService := service.NewCategoryService(mockCategoryRepo, mockEventService)

	tests := []struct {
		name    string
		id      string
		req     *model.CategoryUpdateRequest
		setup   func()
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功更新分类",
			id:   uuid.New().String(),
			req: &model.CategoryUpdateRequest{
				Name:        testutil.StringPtr("更新后的分类"),
				Description: testutil.StringPtr("更新后的描述"),
				SortOrder:   testutil.IntPtr(2),
			},
			setup: func() {
				existingCategory := &model.Category{
					ID:          uuid.New().String(),
					Name:        "原分类",
					Description: testutil.StringPtr("原描述"),
					IsActive:    true,
					SortOrder:   1,
					CreatedAt:   time.Now().Add(-time.Hour),
					UpdatedAt:   time.Now().Add(-time.Hour),
				}

				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(existingCategory, nil).
					Times(1)

				mockCategoryRepo.EXPECT().
					Update(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)

				mockEventService.EXPECT().
					PublishCategoryUpdated(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "更新不存在的分类",
			id:   "non-existent-id",
			req: &model.CategoryUpdateRequest{
				Name: testutil.StringPtr("更新后的分类"),
			},
			setup: func() {
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), "non-existent-id").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "category not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			result, err := categoryService.Update(context.Background(), tt.id, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotEmpty(t, result.ID)
				if tt.req.Name != nil {
					assert.Equal(t, *tt.req.Name, result.Name)
				}
				if tt.req.Description != nil && result.Description != nil {
					assert.Equal(t, *tt.req.Description, *result.Description)
				}
			}
		})
	}
}

func TestCategoryService_UpdateStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCategoryRepo := mocks.NewMockCategoryRepository(ctrl)
	mockEventService := mocks.NewMockEventService(ctrl)

	categoryService := service.NewCategoryService(mockCategoryRepo, mockEventService)

	tests := []struct {
		name     string
		id       string
		isActive bool
		setup    func()
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "成功更新分类状态",
			id:       uuid.New().String(),
			isActive: false,
			setup: func() {
				existingCategory := &model.Category{
					ID:        uuid.New().String(),
					Name:      "测试分类",
					IsActive:  true,
					CreatedAt: time.Now().Add(-time.Hour),
					UpdatedAt: time.Now().Add(-time.Hour),
				}

				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(existingCategory, nil).
					Times(1)

				mockCategoryRepo.EXPECT().
					Update(gomock.Any(), gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name:     "分类不存在",
			id:       "non-existent-id",
			isActive: false,
			setup: func() {
				mockCategoryRepo.EXPECT().
					GetByID(gomock.Any(), "non-existent-id").
					Return(nil, sql.ErrNoRows).
					Times(1)
			},
			wantErr: true,
			errMsg:  "failed to get category",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			err := categoryService.UpdateStatus(context.Background(), tt.id, tt.isActive)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
