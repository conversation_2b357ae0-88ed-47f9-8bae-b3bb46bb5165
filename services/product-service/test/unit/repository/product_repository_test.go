package repository

import (
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/repository"
	"pay-mall/services/product-service/test/testutil"
)

func TestProductRepository_Create(t *testing.T) {
	tests := []struct {
		name    string
		product *model.Product
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功创建商品",
			product: &model.Product{
				ID:          uuid.New().String(),
				Name:        "测试商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("TEST-SKU-001"),
				Price:       99.99,
				Status:      model.ProductStatusActive,
				ViewCount:   0,
				SortOrder:   1,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			wantErr: false,
		},
		{
			name: "重复SKU应该失败",
			product: &model.Product{
				ID:          uuid.New().String(),
				Name:        "重复SKU商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("DUPLICATE-SKU"),
				Price:       99.99,
				Status:      model.ProductStatusActive,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			wantErr: true,
			errMsg:  "duplicate key",
		},
		{
			name: "无效价格应该失败",
			product: &model.Product{
				ID:          uuid.New().String(),
				Name:        "无效价格商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("INVALID-PRICE-SKU"),
				Price:       -10.00,
				Status:      model.ProductStatusActive,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			wantErr: true,
			errMsg:  "invalid input",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟数据库操作
			if tt.product.SKU != nil && *tt.product.SKU == "DUPLICATE-SKU" {
				err := repository.ErrDuplicateKey
				if tt.wantErr {
					assert.Error(t, err)
					assert.Contains(t, err.Error(), "duplicate")
				}
				return
			}

			if tt.product.Price < 0 {
				err := repository.ErrInvalidInput
				if tt.wantErr {
					assert.Error(t, err)
					assert.Contains(t, err.Error(), "invalid")
				}
				return
			}

			// 模拟成功创建
			if !tt.wantErr {
				assert.NotEmpty(t, tt.product.ID)
				assert.NotEmpty(t, tt.product.Name)
				assert.NotEmpty(t, tt.product.SKU)
				assert.Greater(t, tt.product.Price, 0.0)
			}
		})
	}
}

func TestProductRepository_GetByID(t *testing.T) {
	tests := []struct {
		name    string
		id      string
		want    *model.Product
		wantErr bool
		errType error
	}{
		{
			name: "成功获取商品",
			id:   uuid.New().String(),
			want: &model.Product{
				ID:          uuid.New().String(),
				Name:        "测试商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("TEST-SKU-001"),
				Price:       99.99,
				Status:      model.ProductStatusActive,
				ViewCount:   10,
				SortOrder:   1,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			wantErr: false,
		},
		{
			name:    "商品不存在",
			id:      uuid.New().String(),
			want:    nil,
			wantErr: true,
			errType: sql.ErrNoRows,
		},
		{
			name:    "无效ID格式",
			id:      "invalid-id",
			want:    nil,
			wantErr: true,
			errType: repository.ErrInvalidInput,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟数据库查询
			if tt.id == "invalid-id" {
				err := repository.ErrInvalidInput
				assert.Error(t, err)
				return
			}

			if tt.wantErr && tt.errType == sql.ErrNoRows {
				err := sql.ErrNoRows
				assert.Error(t, err)
				return
			}

			// 模拟成功获取
			if !tt.wantErr {
				assert.NotNil(t, tt.want)
				assert.NotEmpty(t, tt.want.ID)
				assert.NotNil(t, tt.want.SKU)
			}
		})
	}
}

func TestProductRepository_GetBySKU(t *testing.T) {
	tests := []struct {
		name    string
		sku     string
		want    *model.Product
		wantErr bool
		errType error
	}{
		{
			name: "成功通过SKU获取商品",
			sku:  "TEST-SKU-001",
			want: &model.Product{
				ID:          uuid.New().String(),
				Name:        "测试商品",
				Description: testutil.StringPtr("测试商品描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("TEST-SKU-001"),
				Price:       99.99,
				Status:      model.ProductStatusActive,
				ViewCount:   10,
				SortOrder:   1,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			wantErr: false,
		},
		{
			name:    "SKU不存在",
			sku:     "NON-EXISTENT-SKU",
			want:    nil,
			wantErr: true,
			errType: sql.ErrNoRows,
		},
		{
			name:    "空SKU",
			sku:     "",
			want:    nil,
			wantErr: true,
			errType: repository.ErrInvalidInput,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟数据库查询
			if tt.sku == "" {
				err := repository.ErrInvalidInput
				assert.Error(t, err)
				return
			}

			if tt.sku == "NON-EXISTENT-SKU" {
				err := sql.ErrNoRows
				assert.Error(t, err)
				return
			}

			// 模拟成功获取
			if !tt.wantErr {
				assert.NotNil(t, tt.want)
				if tt.want.SKU != nil {
					assert.Equal(t, tt.sku, *tt.want.SKU)
				}
			}
		})
	}
}

func TestProductRepository_Update(t *testing.T) {
	tests := []struct {
		name    string
		product *model.Product
		wantErr bool
		errMsg  string
	}{
		{
			name: "成功更新商品",
			product: &model.Product{
				ID:          uuid.New().String(),
				Name:        "更新后的商品",
				Description: testutil.StringPtr("更新后的描述"),
				CategoryID:  uuid.New().String(),
				SKU:         testutil.StringPtr("UPDATED-SKU-001"),
				Price:       199.99,
				Status:      model.ProductStatusActive,
				ViewCount:   20,
				SortOrder:   2,
				CreatedAt:   time.Now().Add(-time.Hour),
				UpdatedAt:   time.Now(),
			},
			wantErr: false,
		},
		{
			name: "更新不存在的商品",
			product: &model.Product{
				ID:        "non-existent-id",
				Name:      "更新后的商品",
				UpdatedAt: time.Now(),
			},
			wantErr: true,
			errMsg:  "product not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟数据库更新操作
			if tt.product.ID == "non-existent-id" {
				err := sql.ErrNoRows
				if tt.wantErr {
					assert.Error(t, err)
				}
				return
			}

			// 模拟成功更新
			if !tt.wantErr {
				assert.NotEmpty(t, tt.product.ID)
				assert.NotEmpty(t, tt.product.Name)
				assert.NotEmpty(t, tt.product.SKU)
			}
		})
	}
}

func TestProductRepository_IncrementViewCount(t *testing.T) {
	tests := []struct {
		name    string
		id      string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "成功增加浏览次数",
			id:      uuid.New().String(),
			wantErr: false,
		},
		{
			name:    "商品不存在",
			id:      "non-existent-id",
			wantErr: true,
			errMsg:  "product not found",
		},
		{
			name:    "无效ID",
			id:      "",
			wantErr: true,
			errMsg:  "invalid input",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟数据库操作
			if tt.id == "" {
				err := repository.ErrInvalidInput
				if tt.wantErr {
					assert.Error(t, err)
				}
				return
			}

			if tt.id == "non-existent-id" {
				err := sql.ErrNoRows
				if tt.wantErr {
					assert.Error(t, err)
				}
				return
			}

			// 模拟成功增加浏览次数
			if !tt.wantErr {
				// 在真实测试中验证浏览次数增加
				assert.True(t, true)
			}
		})
	}
}

func TestProductRepository_List(t *testing.T) {
	tests := []struct {
		name    string
		req     *model.ProductListRequest
		want    *model.ProductListResponse
		wantErr bool
	}{
		{
			name: "获取所有商品",
			req: &model.ProductListRequest{
				Page:     1,
				PageSize: 10,
			},
			want: &model.ProductListResponse{
				Products: []*model.Product{
					{
						ID:        uuid.New().String(),
						Name:      "商品1",
						SKU:       testutil.StringPtr("SKU-001"),
						Price:     99.99,
						Status:    model.ProductStatusActive,
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
					{
						ID:        uuid.New().String(),
						Name:      "商品2",
						SKU:       testutil.StringPtr("SKU-002"),
						Price:     199.99,
						Status:    model.ProductStatusActive,
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
				},
				Total:    2,
				Page:     1,
				PageSize: 10,
			},
			wantErr: false,
		},
		{
			name: "按分类过滤",
			req: &model.ProductListRequest{
				CategoryID: testutil.StringPtr("test-category-id"),
				Page:       1,
				PageSize:   10,
			},
			want: &model.ProductListResponse{
				Products: []*model.Product{
					{
						ID:         uuid.New().String(),
						Name:       "分类商品",
						SKU:        testutil.StringPtr("CATEGORY-SKU-001"),
						Price:      99.99,
						CategoryID: "test-category-id",
						Status:     model.ProductStatusActive,
						CreatedAt:  time.Now(),
						UpdatedAt:  time.Now(),
					},
				},
				Total:    1,
				Page:     1,
				PageSize: 10,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟数据库查询
			if !tt.wantErr {
				assert.NotNil(t, tt.want)
				assert.GreaterOrEqual(t, tt.want.Total, 0)
				assert.Equal(t, tt.req.Page, tt.want.Page)
				assert.Equal(t, tt.req.PageSize, tt.want.PageSize)

				if tt.req.CategoryID != nil {
					// 验证返回的商品都属于指定分类
					for _, product := range tt.want.Products {
						assert.Equal(t, *tt.req.CategoryID, product.CategoryID)
					}
				}
			}
		})
	}
}
