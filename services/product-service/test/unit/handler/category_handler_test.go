package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"pay-mall/services/product-service/internal/handler"
	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/test/mocks"
	"pay-mall/services/product-service/test/testutil"
)

func setupCategoryHandler(t *testing.T) (*gin.Engine, *mocks.MockCategoryService, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockCategoryService := mocks.NewMockCategoryService(ctrl)

	gin.SetMode(gin.TestMode)
	router := gin.New()

	categoryHandler := handler.NewCategoryHandler(mockCategoryService)

	// 设置路由
	v1 := router.Group("/api/v1")
	{
		categories := v1.Group("/categories")
		{
			categories.POST("", categoryHandler.Create)
			categories.GET("/:id", categoryHandler.GetByID)
			categories.PUT("/:id", categoryHandler.Update)
			categories.DELETE("/:id", categoryHandler.Delete)
			categories.GET("", categoryHandler.List)
			categories.GET("/tree", categoryHandler.GetTree)
			categories.PUT("/:id/status", categoryHandler.UpdateStatus)
		}
	}

	return router, mockCategoryService, ctrl
}

func TestCategoryHandler_Create(t *testing.T) {
	router, mockService, ctrl := setupCategoryHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
		expectedBody   map[string]interface{}
	}{
		{
			name: "成功创建分类",
			requestBody: map[string]interface{}{
				"name":        "测试分类",
				"description": "测试分类描述",
				"icon_url":    "https://example.com/icon.png",
				"sort_order":  1,
			},
			setup: func() {
				expectedCategory := &model.Category{
					ID:          uuid.New().String(),
					Name:        "测试分类",
					Description: testutil.StringPtr("测试分类描述"),
					IconURL:     testutil.StringPtr("https://example.com/icon.png"),
					SortOrder:   1,
					IsActive:    true,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}

				mockService.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(expectedCategory, nil).
					Times(1)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name: "无效请求体",
			requestBody: map[string]interface{}{
				"name": "", // 空名称
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "服务层错误",
			requestBody: map[string]interface{}{
				"name":        "测试分类",
				"description": "测试分类描述",
			},
			setup: func() {
				mockService.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil, assert.AnError).
					Times(1)
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/api/v1/categories", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusCreated {
				var response model.Category
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.NotEmpty(t, response.ID)
				assert.Equal(t, "测试分类", response.Name)
			}
		})
	}
}

func TestCategoryHandler_GetByID(t *testing.T) {
	router, mockService, ctrl := setupCategoryHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		categoryID     string
		setup          func()
		expectedStatus int
	}{
		{
			name:       "成功获取分类",
			categoryID: uuid.New().String(),
			setup: func() {
				expectedCategory := &model.Category{
					ID:          uuid.New().String(),
					Name:        "测试分类",
					Description: testutil.StringPtr("测试分类描述"),
					IsActive:    true,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}

				mockService.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(expectedCategory, nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:       "分类不存在",
			categoryID: uuid.New().String(),
			setup: func() {
				mockService.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(nil, assert.AnError).
					Times(1)
			},
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "无效ID",
			categoryID:     "",
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			url := "/api/v1/categories/" + tt.categoryID
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response model.Category
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.NotEmpty(t, response.ID)
				assert.NotEmpty(t, response.Name)
			}
		})
	}
}

func TestCategoryHandler_Update(t *testing.T) {
	router, mockService, ctrl := setupCategoryHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		categoryID     string
		requestBody    interface{}
		setup          func()
		expectedStatus int
	}{
		{
			name:       "成功更新分类",
			categoryID: uuid.New().String(),
			requestBody: map[string]interface{}{
				"name":        "更新后的分类",
				"description": "更新后的描述",
				"sort_order":  2,
			},
			setup: func() {
				updatedCategory := &model.Category{
					ID:          uuid.New().String(),
					Name:        "更新后的分类",
					Description: testutil.StringPtr("更新后的描述"),
					SortOrder:   2,
					IsActive:    true,
					CreatedAt:   time.Now().Add(-time.Hour),
					UpdatedAt:   time.Now(),
				}

				mockService.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(updatedCategory, nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:       "分类不存在",
			categoryID: "non-existent-id",
			requestBody: map[string]interface{}{
				"name": "更新后的分类",
			},
			setup: func() {
				mockService.EXPECT().
					Update(gomock.Any(), "non-existent-id", gomock.Any()).
					Return(nil, assert.AnError).
					Times(1)
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			body, _ := json.Marshal(tt.requestBody)
			url := "/api/v1/categories/" + tt.categoryID
			req := httptest.NewRequest(http.MethodPut, url, bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response model.Category
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.NotEmpty(t, response.ID)
			}
		})
	}
}

func TestCategoryHandler_UpdateStatus(t *testing.T) {
	router, mockService, ctrl := setupCategoryHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		categoryID     string
		requestBody    interface{}
		setup          func()
		expectedStatus int
	}{
		{
			name:       "成功更新分类状态",
			categoryID: uuid.New().String(),
			requestBody: map[string]interface{}{
				"is_active": false,
			},
			setup: func() {
				mockService.EXPECT().
					UpdateStatus(gomock.Any(), gomock.Any(), false).
					Return(nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:       "分类不存在",
			categoryID: "non-existent-id",
			requestBody: map[string]interface{}{
				"is_active": false,
			},
			setup: func() {
				mockService.EXPECT().
					UpdateStatus(gomock.Any(), "non-existent-id", false).
					Return(assert.AnError).
					Times(1)
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:       "无效请求体",
			categoryID: uuid.New().String(),
			requestBody: map[string]interface{}{
				"invalid_field": "value",
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			body, _ := json.Marshal(tt.requestBody)
			url := "/api/v1/categories/" + tt.categoryID + "/status"
			req := httptest.NewRequest(http.MethodPut, url, bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func TestCategoryHandler_List(t *testing.T) {
	router, mockService, ctrl := setupCategoryHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		queryParams    string
		setup          func()
		expectedStatus int
	}{
		{
			name:        "成功获取分类列表",
			queryParams: "?page=1&page_size=10",
			setup: func() {
				expectedResponse := &model.CategoryListResponse{
					Categories: []*model.Category{
						{
							ID:        uuid.New().String(),
							Name:      "分类1",
							IsActive:  true,
							CreatedAt: time.Now(),
							UpdatedAt: time.Now(),
						},
						{
							ID:        uuid.New().String(),
							Name:      "分类2",
							IsActive:  true,
							CreatedAt: time.Now(),
							UpdatedAt: time.Now(),
						},
					},
					Total:    2,
					Page:     1,
					PageSize: 10,
				}

				mockService.EXPECT().
					List(gomock.Any(), gomock.Any()).
					Return(expectedResponse, nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:        "按状态过滤",
			queryParams: "?is_active=true&page=1&page_size=10",
			setup: func() {
				expectedResponse := &model.CategoryListResponse{
					Categories: []*model.Category{
						{
							ID:        uuid.New().String(),
							Name:      "活跃分类",
							IsActive:  true,
							CreatedAt: time.Now(),
							UpdatedAt: time.Now(),
						},
					},
					Total:    1,
					Page:     1,
					PageSize: 10,
				}

				mockService.EXPECT().
					List(gomock.Any(), gomock.Any()).
					Return(expectedResponse, nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			url := "/api/v1/categories" + tt.queryParams
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response model.CategoryListResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.GreaterOrEqual(t, response.Total, 0)
				assert.GreaterOrEqual(t, len(response.Categories), 0)
			}
		})
	}
}
