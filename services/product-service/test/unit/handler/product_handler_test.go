package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"pay-mall/services/product-service/internal/handler"
	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/test/mocks"
	"pay-mall/services/product-service/test/testutil"
)

func setupProductHandler(t *testing.T) (*gin.Engine, *mocks.MockProductService, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockProductService := mocks.NewMockProductService(ctrl)

	gin.SetMode(gin.TestMode)
	router := gin.New()

	productHandler := handler.NewProductHandler(mockProductService)

	// 设置路由
	v1 := router.Group("/api/v1")
	{
		products := v1.Group("/products")
		{
			products.POST("", productHandler.Create)
			products.GET("/:id", productHandler.GetByID)
			products.PUT("/:id", productHandler.Update)
			products.DELETE("/:id", productHandler.Delete)
			products.GET("", productHandler.List)
			products.GET("/search", productHandler.Search)
			products.PUT("/:id/status", productHandler.UpdateStatus)

		}
	}

	return router, mockProductService, ctrl
}

func TestProductHandler_Create(t *testing.T) {
	router, mockService, ctrl := setupProductHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
	}{
		{
			name: "成功创建商品",
			requestBody: map[string]interface{}{
				"name":        "测试商品",
				"description": "测试商品描述",
				"category_id": uuid.New().String(),
				"sku":         "TEST-SKU-001",
				"price":       99.99,
				"stock":       100,
				"sort_order":  1,
			},
			setup: func() {
				expectedProduct := &model.Product{
					ID:          uuid.New().String(),
					Name:        "测试商品",
					Description: testutil.StringPtr("测试商品描述"),
					CategoryID:  uuid.New().String(),
					SKU:         testutil.StringPtr("TEST-SKU-001"),
					Price:       99.99,
					Status:      model.ProductStatusActive,
					SortOrder:   1,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}

				mockService.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(expectedProduct, nil).
					Times(1)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name: "无效请求体",
			requestBody: map[string]interface{}{
				"name": "", // 空名称
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "服务层错误",
			requestBody: map[string]interface{}{
				"name":        "测试商品",
				"description": "测试商品描述",
				"category_id": uuid.New().String(),
				"sku":         "TEST-SKU-001",
				"price":       99.99,
				"stock":       100,
			},
			setup: func() {
				mockService.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil, assert.AnError).
					Times(1)
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/api/v1/products", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusCreated {
				var response model.Product
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.NotEmpty(t, response.ID)
				assert.Equal(t, "测试商品", response.Name)
				assert.Equal(t, "TEST-SKU-001", *response.SKU)
			}
		})
	}
}

func TestProductHandler_GetByID(t *testing.T) {
	router, mockService, ctrl := setupProductHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		productID      string
		setup          func()
		expectedStatus int
	}{
		{
			name:      "成功获取商品",
			productID: uuid.New().String(),
			setup: func() {
				expectedProduct := &model.Product{
					ID:          uuid.New().String(),
					Name:        "测试商品",
					Description: testutil.StringPtr("测试商品描述"),
					SKU:         testutil.StringPtr("TEST-SKU-001"),
					Price:       99.99,
					Status:      model.ProductStatusActive,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}

				mockService.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(expectedProduct, nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "商品不存在",
			productID: uuid.New().String(),
			setup: func() {
				mockService.EXPECT().
					GetByID(gomock.Any(), gomock.Any()).
					Return(nil, assert.AnError).
					Times(1)
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "无效ID",
			productID:      "",
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			url := "/api/v1/products/" + tt.productID
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response model.Product
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.NotEmpty(t, response.ID)
				assert.NotEmpty(t, response.Name)
				assert.NotEmpty(t, response.SKU)
			}
		})
	}
}

func TestProductHandler_List(t *testing.T) {
	router, mockService, ctrl := setupProductHandler(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		queryParams    string
		setup          func()
		expectedStatus int
	}{
		{
			name:        "成功获取商品列表",
			queryParams: "?page=1&page_size=10",
			setup: func() {
				expectedResponse := &model.ProductListResponse{
					Products: []*model.Product{
						{
							ID:        uuid.New().String(),
							Name:      "商品1",
							SKU:       testutil.StringPtr("SKU-001"),
							Price:     99.99,
							Status:    model.ProductStatusActive,
							CreatedAt: time.Now(),
							UpdatedAt: time.Now(),
						},
						{
							ID:        uuid.New().String(),
							Name:      "商品2",
							SKU:       testutil.StringPtr("SKU-002"),
							Price:     199.99,
							Status:    model.ProductStatusActive,
							CreatedAt: time.Now(),
							UpdatedAt: time.Now(),
						},
					},
					Total:    2,
					Page:     1,
					PageSize: 10,
				}

				mockService.EXPECT().
					List(gomock.Any(), gomock.Any()).
					Return(expectedResponse, nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:        "按分类过滤",
			queryParams: "?category_id=" + uuid.New().String() + "&page=1&page_size=10",
			setup: func() {
				expectedResponse := &model.ProductListResponse{
					Products: []*model.Product{
						{
							ID:         uuid.New().String(),
							Name:       "分类商品",
							SKU:        testutil.StringPtr("CATEGORY-SKU-001"),
							Price:      99.99,
							CategoryID: uuid.New().String(),
							Status:     model.ProductStatusActive,
							CreatedAt:  time.Now(),
							UpdatedAt:  time.Now(),
						},
					},
					Total:    1,
					Page:     1,
					PageSize: 10,
				}

				mockService.EXPECT().
					List(gomock.Any(), gomock.Any()).
					Return(expectedResponse, nil).
					Times(1)
			},
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			// 准备请求
			url := "/api/v1/products" + tt.queryParams
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response model.ProductListResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.GreaterOrEqual(t, response.Total, 0)
				assert.GreaterOrEqual(t, len(response.Products), 0)
			}
		})
	}
}
