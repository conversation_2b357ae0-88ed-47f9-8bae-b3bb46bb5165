server:
  host: "0.0.0.0"
  port: "8083"
  mode: "debug" # debug, release, test

database:
  host: "127.0.0.1"
  port: 3306
  username: "root"
  password: "123456"
  database: "product_service"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600 # seconds

redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  pool_size: 10
  min_idle_conns: 5

kafka:
  brokers:
    - "localhost:9092"
  topics:
    product_events: "product-events"
  producer:
    retry_max: 3
    batch_size: 100
    batch_timeout: 10 # milliseconds
  consumer:
    group_id: "product-service"
    auto_offset_reset: "earliest"

cache:
  product_detail_ttl: 3600 # 1 hour
  category_tree_ttl: 86400 # 24 hours
  search_result_ttl: 1800 # 30 minutes
  product_list_ttl: 900 # 15 minutes

search:
  page_size_default: 20
  page_size_max: 100
  
logging:
  level: "info"
  format: "json"
  output: "stdout"
