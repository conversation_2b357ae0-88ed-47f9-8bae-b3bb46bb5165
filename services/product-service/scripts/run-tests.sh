#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_message $RED "错误: Docker 未运行，请启动 Docker"
        exit 1
    fi
}

# 启动测试环境
start_test_env() {
    print_message $BLUE "启动测试环境..."
    
    # 停止并清理现有容器
    docker-compose -f docker-compose.test.yml down -v > /dev/null 2>&1 || true
    
    # 启动测试环境
    docker-compose -f docker-compose.test.yml up -d
    
    print_message $YELLOW "等待服务启动..."
    
    # 等待 MySQL 启动
    print_message $YELLOW "等待 MySQL 启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose -f docker-compose.test.yml exec -T mysql mysqladmin ping -h localhost --silent; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_message $RED "MySQL 启动超时"
        exit 1
    fi
    
    # 等待 Kafka 启动
    print_message $YELLOW "等待 Kafka 启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose -f docker-compose.test.yml exec -T kafka kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_message $RED "Kafka 启动超时"
        exit 1
    fi
    
    print_message $GREEN "测试环境启动完成"
}

# 停止测试环境
stop_test_env() {
    print_message $BLUE "停止测试环境..."
    docker-compose -f docker-compose.test.yml down -v
    print_message $GREEN "测试环境已停止"
}

# 运行单元测试
run_unit_tests() {
    print_message $BLUE "运行单元测试..."
    
    # 运行 Repository 层测试
    print_message $YELLOW "运行 Repository 层测试..."
    go test -v ./test/unit/repository/... -short
    
    # 运行 Service 层测试
    print_message $YELLOW "运行 Service 层测试..."
    go test -v ./test/unit/service/... -short
    
    # 运行 Handler 层测试
    print_message $YELLOW "运行 Handler 层测试..."
    go test -v ./test/unit/handler/... -short
    
    print_message $GREEN "单元测试完成"
}

# 运行集成测试
run_integration_tests() {
    print_message $BLUE "运行集成测试..."
    
    # 设置测试环境变量
    export TEST_DB_HOST=localhost
    export TEST_DB_PORT=3307
    export TEST_DB_USER=testuser
    export TEST_DB_PASSWORD=testpassword
    export TEST_DB_NAME=product_service_test
    export TEST_KAFKA_BROKER=localhost:9092
    
    # 运行 Kafka 集成测试
    print_message $YELLOW "运行 Kafka 集成测试..."
    go test -v ./test/integration/kafka_test.go -timeout=5m
    
    # 运行 API 集成测试
    print_message $YELLOW "运行 API 集成测试..."
    go test -v ./test/integration/api_test.go -timeout=5m
    
    print_message $GREEN "集成测试完成"
}

# 运行所有测试
run_all_tests() {
    print_message $BLUE "运行所有测试..."
    
    # 运行单元测试
    run_unit_tests
    
    # 启动测试环境
    start_test_env
    
    # 运行集成测试
    run_integration_tests
    
    # 停止测试环境
    stop_test_env
    
    print_message $GREEN "所有测试完成"
}

# 生成测试覆盖率报告
run_coverage() {
    print_message $BLUE "生成测试覆盖率报告..."
    
    # 创建覆盖率目录
    mkdir -p coverage
    
    # 运行单元测试并生成覆盖率
    go test -v ./test/unit/... -short -coverprofile=coverage/unit.out -covermode=atomic
    
    # 启动测试环境
    start_test_env
    
    # 运行集成测试并生成覆盖率
    export TEST_DB_HOST=localhost
    export TEST_DB_PORT=3307
    export TEST_DB_USER=testuser
    export TEST_DB_PASSWORD=testpassword
    export TEST_DB_NAME=product_service_test
    export TEST_KAFKA_BROKER=localhost:9092
    
    go test -v ./test/integration/... -coverprofile=coverage/integration.out -covermode=atomic -timeout=5m
    
    # 合并覆盖率文件
    echo "mode: atomic" > coverage/total.out
    tail -n +2 coverage/unit.out >> coverage/total.out
    tail -n +2 coverage/integration.out >> coverage/total.out
    
    # 生成 HTML 报告
    go tool cover -html=coverage/total.out -o coverage/coverage.html
    
    # 显示覆盖率统计
    go tool cover -func=coverage/total.out
    
    # 停止测试环境
    stop_test_env
    
    print_message $GREEN "覆盖率报告已生成: coverage/coverage.html"
}

# 清理测试环境
cleanup() {
    print_message $BLUE "清理测试环境..."
    docker-compose -f docker-compose.test.yml down -v --remove-orphans
    docker system prune -f
    print_message $GREEN "清理完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  unit        运行单元测试"
    echo "  integration 运行集成测试"
    echo "  all         运行所有测试"
    echo "  coverage    生成测试覆盖率报告"
    echo "  start       启动测试环境"
    echo "  stop        停止测试环境"
    echo "  cleanup     清理测试环境"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 unit        # 运行单元测试"
    echo "  $0 all         # 运行所有测试"
    echo "  $0 coverage    # 生成覆盖率报告"
}

# 主函数
main() {
    case "${1:-all}" in
        unit)
            run_unit_tests
            ;;
        integration)
            check_docker
            start_test_env
            run_integration_tests
            stop_test_env
            ;;
        all)
            check_docker
            run_all_tests
            ;;
        coverage)
            check_docker
            run_coverage
            ;;
        start)
            check_docker
            start_test_env
            ;;
        stop)
            stop_test_env
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号，确保清理
trap 'print_message $YELLOW "收到中断信号，正在清理..."; stop_test_env; exit 1' INT TERM

# 运行主函数
main "$@"
