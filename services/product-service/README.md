# 商品服务 (Product Service)

商品服务是支付商城微服务架构中的核心服务之一，负责商品和分类的管理。

## 功能特性

### 分类管理
- ✅ 分层分类结构支持
- ✅ 分类CRUD操作
- ✅ 分类树形结构查询
- ✅ 分类路径计算
- ✅ 分类状态管理

### 商品管理
- ✅ 商品CRUD操作
- ✅ 商品多规格变体支持
- ✅ 商品状态管理
- ✅ 商品搜索和筛选
- ✅ 商品浏览统计
- ✅ 热门商品推荐

### 技术特性
- ✅ Redis缓存集成
- ✅ Kafka事件发布
- ✅ 全文搜索支持
- ✅ RESTful API设计
- ✅ Swagger API文档
- ✅ Docker容器化部署

## 技术栈

- **语言**: Go 1.21+
- **框架**: Gin (HTTP) + gRPC
- **数据库**: MySQL 8.0
- **缓存**: Redis 7
- **消息队列**: Kafka
- **文档**: Swagger/OpenAPI
- **容器化**: Docker + Docker Compose

## 项目结构

```
services/product-service/
├── cmd/                    # 应用入口
│   └── main.go
├── internal/               # 内部代码
│   ├── config/            # 配置管理
│   ├── database/          # 数据库连接
│   ├── event/             # 事件定义
│   ├── handler/           # HTTP处理器
│   ├── middleware/        # 中间件
│   ├── model/             # 数据模型
│   ├── repository/        # 数据访问层
│   ├── router/            # 路由配置
│   └── service/           # 业务逻辑层
├── configs/               # 配置文件
├── scripts/               # 脚本文件
├── Dockerfile             # Docker构建文件
├── docker-compose.yml     # Docker Compose配置
├── go.mod                 # Go模块文件
└── README.md              # 项目文档
```

## 快速开始

### 环境要求

- Go 1.21+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7+
- Kafka

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd pay-mall/services/product-service
```

2. **安装依赖**
```bash
go mod download
```

3. **启动依赖服务**
```bash
docker-compose up -d mysql redis kafka
```

4. **配置环境变量**
```bash
cp configs/config.example.yaml configs/config.yaml
# 编辑配置文件
```

5. **运行服务**
```bash
go run cmd/main.go
```

### Docker部署

1. **构建并启动所有服务**
```bash
docker-compose up -d
```

2. **查看服务状态**
```bash
docker-compose ps
```

3. **查看日志**
```bash
docker-compose logs -f product-service
```

## API文档

服务启动后，可以通过以下地址访问API文档：

- Swagger UI: http://localhost:8080/swagger/index.html
- 健康检查: http://localhost:8080/health

### 主要API端点

#### 分类管理
- `GET /api/v1/categories` - 获取分类列表
- `POST /api/v1/categories` - 创建分类
- `GET /api/v1/categories/{id}` - 获取分类详情
- `PUT /api/v1/categories/{id}` - 更新分类
- `DELETE /api/v1/categories/{id}` - 删除分类
- `GET /api/v1/categories/tree` - 获取分类树
- `GET /api/v1/categories/{id}/children` - 获取子分类
- `GET /api/v1/categories/{id}/path` - 获取分类路径

#### 商品管理
- `GET /api/v1/products` - 获取商品列表
- `POST /api/v1/products` - 创建商品
- `GET /api/v1/products/{id}` - 获取商品详情
- `PUT /api/v1/products/{id}` - 更新商品
- `DELETE /api/v1/products/{id}` - 删除商品
- `GET /api/v1/products/search` - 搜索商品
- `GET /api/v1/products/popular` - 获取热门商品
- `GET /api/v1/products/sku/{sku}` - 根据SKU获取商品
- `GET /api/v1/products/category/{category_id}` - 根据分类获取商品

## 配置说明

### 数据库配置
```yaml
database:
  host: localhost
  port: 3306
  user: root
  password: password
  name: pay_mall_product
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600
```

### Redis配置
```yaml
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10
```

### Kafka配置
```yaml
kafka:
  brokers:
    - localhost:9092
  topics:
    product_events: product.events
  producer:
    retry_max: 3
    return_successes: true
```

## 事件系统

服务会发布以下事件到Kafka：

- `ProductCreated` - 商品创建事件
- `ProductUpdated` - 商品更新事件
- `ProductDeleted` - 商品删除事件
- `ProductStatusChanged` - 商品状态变更事件
- `CategoryCreated` - 分类创建事件
- `CategoryUpdated` - 分类更新事件
- `CategoryDeleted` - 分类删除事件

## 缓存策略

### 缓存键规则
- 商品: `product:{id}`
- 分类: `category:{id}`
- 商品列表: `product:list:{hash}`
- 搜索结果: `product:search:{hash}`

### 缓存TTL
- 商品详情: 1小时
- 分类信息: 2小时
- 列表数据: 30分钟
- 搜索结果: 15分钟

## 监控和日志

### 健康检查
- 端点: `GET /health`
- 返回服务状态和依赖检查结果

### 日志格式
使用结构化日志，包含请求ID、用户ID、操作类型等信息。

### 指标监控
- 请求响应时间
- 错误率统计
- 缓存命中率
- 数据库连接池状态

## 开发指南

### 代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 添加必要的注释和文档

### 测试
```bash
# 运行单元测试
go test ./...

# 运行集成测试
go test -tags=integration ./...

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### 数据库迁移
```bash
# 创建新的迁移文件
migrate create -ext sql -dir migrations -seq add_new_table

# 执行迁移
migrate -path migrations -database "mysql://user:pass@tcp(localhost:3306)/dbname" up

# 回滚迁移
migrate -path migrations -database "mysql://user:pass@tcp(localhost:3306)/dbname" down 1
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接配置是否正确
   - 确认网络连通性

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置
   - 检查防火墙设置

3. **Kafka连接失败**
   - 确认Kafka和Zookeeper服务状态
   - 检查broker地址配置
   - 验证topic是否存在

### 日志查看
```bash
# 查看应用日志
docker-compose logs -f product-service

# 查看数据库日志
docker-compose logs -f mysql

# 查看Redis日志
docker-compose logs -f redis
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。详情请参阅LICENSE文件。
