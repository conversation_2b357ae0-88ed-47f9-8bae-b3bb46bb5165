version: "2"
sql:
  - engine: "mysql"
    queries: "sql/queries"
    schema: "sql/schema"
    gen:
      go:
        package: "db"
        out: "internal/db"
        sql_package: "database/sql"
        emit_json_tags: true
        emit_db_tags: true
        emit_prepared_queries: false
        emit_interface: true
        emit_exact_table_names: false
        emit_empty_slices: true
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_pointers_for_null_types: false
        emit_enum_valid_method: true
        emit_all_enum_values: true
        overrides:
          - column: "*.created_at"
            go_type: "time.Time"
          - column: "*.updated_at"
            go_type: "time.Time"
          - column: "*.image_urls"
            go_type: "json.RawMessage"
          - column: "*.dimensions"
            go_type: "json.RawMessage"
          - column: "*.tags"
            go_type: "json.RawMessage"
          - column: "*.attributes"
            go_type: "json.RawMessage"
