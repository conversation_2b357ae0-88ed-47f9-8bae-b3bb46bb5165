package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"pay-mall/services/product-service/internal/db"
	"pay-mall/services/product-service/internal/model"
)

// ProductRepository 商品数据访问接口
type ProductRepository interface {
	Create(ctx context.Context, product *model.Product) error
	GetByID(ctx context.Context, id string) (*model.Product, error)
	GetBySKU(ctx context.Context, sku string) (*model.Product, error)
	List(ctx context.Context, req *model.ProductListRequest) ([]*model.Product, int, error)
	Search(ctx context.Context, req *model.ProductSearchRequest) ([]*model.Product, int, error)
	Update(ctx context.Context, product *model.Product) error
	Delete(ctx context.Context, id string) error
	UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error
	IncrementViewCount(ctx context.Context, id string) error
	IncrementSaleCount(ctx context.Context, id string, count int) error
	GetPopular(ctx context.Context, limit int) ([]*model.Product, error)
	GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error)
}

// sqlcProductRepository sqlc实现的商品数据访问
type sqlcProductRepository struct {
	db      *sql.DB
	queries *db.Queries
}

// NewProductRepository 创建商品仓库实例
func NewProductRepository(database *sql.DB) ProductRepository {
	return &sqlcProductRepository{
		db:      database,
		queries: db.New(database),
	}
}

// Create 创建商品
func (r *sqlcProductRepository) Create(ctx context.Context, product *model.Product) error {
	var originalPrice sql.NullString
	if product.OriginalPrice != nil {
		originalPrice = sql.NullString{String: fmt.Sprintf("%.2f", *product.OriginalPrice), Valid: true}
	}

	var imageURL sql.NullString
	if product.ImageURL != nil {
		imageURL = sql.NullString{String: *product.ImageURL, Valid: true}
	}

	var imageURLs json.RawMessage
	if product.ImageURLs != nil {
		imageURLs, _ = json.Marshal(product.ImageURLs)
	}

	var brand sql.NullString
	if product.Brand != nil {
		brand = sql.NullString{String: *product.Brand, Valid: true}
	}

	var sku sql.NullString
	if product.SKU != nil {
		sku = sql.NullString{String: *product.SKU, Valid: true}
	}

	var weight sql.NullString
	if product.Weight != nil {
		weight = sql.NullString{String: fmt.Sprintf("%.3f", *product.Weight), Valid: true}
	}

	var dimensions json.RawMessage
	if product.Dimensions != nil {
		dimensions, _ = json.Marshal(product.Dimensions)
	}

	var tags json.RawMessage
	if product.Tags != nil {
		tags, _ = json.Marshal(product.Tags)
	}

	var attributes json.RawMessage
	if product.Attributes != nil {
		attributes, _ = json.Marshal(product.Attributes)
	}

	var description sql.NullString
	if product.Description != nil {
		description = sql.NullString{String: *product.Description, Valid: true}
	}

	return r.queries.CreateProduct(ctx, db.CreateProductParams{
		ID:            product.ID,
		Name:          product.Name,
		Description:   description,
		Price:         fmt.Sprintf("%.2f", product.Price),
		OriginalPrice: originalPrice,
		ImageUrl:      imageURL,
		ImageUrls:     imageURLs,
		CategoryID:    product.CategoryID,
		Brand:         brand,
		Sku:           sku,
		Weight:        weight,
		Dimensions:    dimensions,
		Tags:          tags,
		Attributes:    attributes,
		Status:        db.ProductsStatus(product.Status),
		SortOrder:     int32(product.SortOrder),
		ViewCount:     int32(product.ViewCount),
		SaleCount:     int32(product.SaleCount),
		CreatedAt:     product.CreatedAt,
		UpdatedAt:     product.UpdatedAt,
	})
}

// GetByID 根据ID获取商品
func (r *sqlcProductRepository) GetByID(ctx context.Context, id string) (*model.Product, error) {
	dbProduct, err := r.queries.GetProductByID(ctx, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product not found")
		}
		return nil, err
	}

	return r.dbProductToModel(dbProduct), nil
}

// GetBySKU 根据SKU获取商品
func (r *sqlcProductRepository) GetBySKU(ctx context.Context, sku string) (*model.Product, error) {
	dbProduct, err := r.queries.GetProductBySKU(ctx, sql.NullString{String: sku, Valid: true})
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product not found")
		}
		return nil, err
	}

	return r.dbProductToModel(dbProduct), nil
}

// List 获取商品列表
func (r *sqlcProductRepository) List(ctx context.Context, req *model.ProductListRequest) ([]*model.Product, int, error) {
	// 获取总数
	total, err := r.queries.CountProducts(ctx)
	if err != nil {
		return nil, 0, err
	}

	// 获取商品列表
	offset := (req.Page - 1) * req.PageSize
	dbProducts, err := r.queries.ListProducts(ctx, db.ListProductsParams{
		Limit:  int32(req.PageSize),
		Offset: int32(offset),
	})
	if err != nil {
		return nil, 0, err
	}

	products := make([]*model.Product, len(dbProducts))
	for i, dbProduct := range dbProducts {
		products[i] = r.dbProductToModel(dbProduct)
	}

	return products, int(total), nil
}

// Search 搜索商品
func (r *sqlcProductRepository) Search(ctx context.Context, req *model.ProductSearchRequest) ([]*model.Product, int, error) {
	searchTerm := "%" + req.Query + "%"

	// 获取总数
	total, err := r.queries.CountSearchProducts(ctx, db.CountSearchProductsParams{
		Name:        searchTerm,
		Description: sql.NullString{String: searchTerm, Valid: true},
		Brand:       sql.NullString{String: searchTerm, Valid: true},
	})
	if err != nil {
		return nil, 0, err
	}

	// 搜索商品
	offset := (req.Page - 1) * req.PageSize
	dbProducts, err := r.queries.SearchProducts(ctx, db.SearchProductsParams{
		Name:        searchTerm,
		Description: sql.NullString{String: searchTerm, Valid: true},
		Brand:       sql.NullString{String: searchTerm, Valid: true},
		Limit:       int32(req.PageSize),
		Offset:      int32(offset),
	})
	if err != nil {
		return nil, 0, err
	}

	products := make([]*model.Product, len(dbProducts))
	for i, dbProduct := range dbProducts {
		products[i] = r.dbProductToModel(dbProduct)
	}

	return products, int(total), nil
}

// Update 更新商品
func (r *sqlcProductRepository) Update(ctx context.Context, product *model.Product) error {
	var originalPrice sql.NullString
	if product.OriginalPrice != nil {
		originalPrice = sql.NullString{String: fmt.Sprintf("%.2f", *product.OriginalPrice), Valid: true}
	}

	var imageURL sql.NullString
	if product.ImageURL != nil {
		imageURL = sql.NullString{String: *product.ImageURL, Valid: true}
	}

	var imageURLs json.RawMessage
	if product.ImageURLs != nil {
		imageURLs, _ = json.Marshal(product.ImageURLs)
	}

	var brand sql.NullString
	if product.Brand != nil {
		brand = sql.NullString{String: *product.Brand, Valid: true}
	}

	var weight sql.NullString
	if product.Weight != nil {
		weight = sql.NullString{String: fmt.Sprintf("%.3f", *product.Weight), Valid: true}
	}

	var dimensions json.RawMessage
	if product.Dimensions != nil {
		dimensions, _ = json.Marshal(product.Dimensions)
	}

	var tags json.RawMessage
	if product.Tags != nil {
		tags, _ = json.Marshal(product.Tags)
	}

	var attributes json.RawMessage
	if product.Attributes != nil {
		attributes, _ = json.Marshal(product.Attributes)
	}

	var description sql.NullString
	if product.Description != nil {
		description = sql.NullString{String: *product.Description, Valid: true}
	}

	return r.queries.UpdateProduct(ctx, db.UpdateProductParams{
		Name:          product.Name,
		Description:   description,
		Price:         fmt.Sprintf("%.2f", product.Price),
		OriginalPrice: originalPrice,
		ImageUrl:      imageURL,
		ImageUrls:     imageURLs,
		CategoryID:    product.CategoryID,
		Brand:         brand,
		Weight:        weight,
		Dimensions:    dimensions,
		Tags:          tags,
		Attributes:    attributes,
		SortOrder:     int32(product.SortOrder),
		UpdatedAt:     time.Now(),
		ID:            product.ID,
	})
}

// Delete 删除商品
func (r *sqlcProductRepository) Delete(ctx context.Context, id string) error {
	return r.queries.DeleteProduct(ctx, db.DeleteProductParams{
		UpdatedAt: time.Now(),
		ID:        id,
	})
}

// UpdateStatus 更新商品状态
func (r *sqlcProductRepository) UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error {
	return r.queries.UpdateProductStatus(ctx, db.UpdateProductStatusParams{
		Status:    db.ProductsStatus(status),
		UpdatedAt: time.Now(),
		ID:        id,
	})
}

// IncrementViewCount 增加浏览次数
func (r *sqlcProductRepository) IncrementViewCount(ctx context.Context, id string) error {
	return r.queries.IncrementViewCount(ctx, db.IncrementViewCountParams{
		UpdatedAt: time.Now(),
		ID:        id,
	})
}

// IncrementSaleCount 增加销售次数
func (r *sqlcProductRepository) IncrementSaleCount(ctx context.Context, id string, count int) error {
	return r.queries.IncrementSaleCount(ctx, db.IncrementSaleCountParams{
		SaleCount: int32(count),
		UpdatedAt: time.Now(),
		ID:        id,
	})
}

// GetPopular 获取热门商品
func (r *sqlcProductRepository) GetPopular(ctx context.Context, limit int) ([]*model.Product, error) {
	dbProducts, err := r.queries.GetPopularProducts(ctx, int32(limit))
	if err != nil {
		return nil, err
	}

	products := make([]*model.Product, len(dbProducts))
	for i, dbProduct := range dbProducts {
		products[i] = r.dbProductToModel(dbProduct)
	}

	return products, nil
}

// GetByCategory 根据分类获取商品
func (r *sqlcProductRepository) GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error) {
	dbProducts, err := r.queries.GetProductsByCategory(ctx, db.GetProductsByCategoryParams{
		CategoryID: categoryID,
		Limit:      int32(limit),
	})
	if err != nil {
		return nil, err
	}

	products := make([]*model.Product, len(dbProducts))
	for i, dbProduct := range dbProducts {
		products[i] = r.dbProductToModel(dbProduct)
	}

	return products, nil
}

// dbProductToModel 将数据库商品转换为模型商品
func (r *sqlcProductRepository) dbProductToModel(dbProduct *db.Product) *model.Product {
	product := &model.Product{
		ID:         dbProduct.ID,
		Name:       dbProduct.Name,
		CategoryID: dbProduct.CategoryID,
		Status:     model.ProductStatus(dbProduct.Status),
		SortOrder:  int(dbProduct.SortOrder),
		ViewCount:  int(dbProduct.ViewCount),
		SaleCount:  int(dbProduct.SaleCount),
		CreatedAt:  dbProduct.CreatedAt,
		UpdatedAt:  dbProduct.UpdatedAt,
	}

	// 解析价格
	if price, err := strconv.ParseFloat(dbProduct.Price, 64); err == nil {
		product.Price = price
	}

	if dbProduct.OriginalPrice.Valid {
		if originalPrice, err := strconv.ParseFloat(dbProduct.OriginalPrice.String, 64); err == nil {
			product.OriginalPrice = &originalPrice
		}
	}

	if dbProduct.Description.Valid {
		product.Description = &dbProduct.Description.String
	}

	if dbProduct.ImageUrl.Valid {
		product.ImageURL = &dbProduct.ImageUrl.String
	}

	if len(dbProduct.ImageUrls) > 0 {
		var imageURLs []interface{}
		if err := json.Unmarshal(dbProduct.ImageUrls, &imageURLs); err == nil {
			jsonArray := model.JSONArray(imageURLs)
			product.ImageURLs = &jsonArray
		}
	}

	if dbProduct.Brand.Valid {
		product.Brand = &dbProduct.Brand.String
	}

	if dbProduct.Sku.Valid {
		product.SKU = &dbProduct.Sku.String
	}

	if dbProduct.Weight.Valid {
		if weight, err := strconv.ParseFloat(dbProduct.Weight.String, 64); err == nil {
			product.Weight = &weight
		}
	}

	if len(dbProduct.Dimensions) > 0 {
		var dimensions map[string]interface{}
		if err := json.Unmarshal(dbProduct.Dimensions, &dimensions); err == nil {
			jsonMap := model.JSONMap(dimensions)
			product.Dimensions = &jsonMap
		}
	}

	if len(dbProduct.Tags) > 0 {
		var tags []interface{}
		if err := json.Unmarshal(dbProduct.Tags, &tags); err == nil {
			jsonArray := model.JSONArray(tags)
			product.Tags = &jsonArray
		}
	}

	if len(dbProduct.Attributes) > 0 {
		var attributes map[string]interface{}
		if err := json.Unmarshal(dbProduct.Attributes, &attributes); err == nil {
			jsonMap := model.JSONMap(attributes)
			product.Attributes = &jsonMap
		}
	}

	return product
}
