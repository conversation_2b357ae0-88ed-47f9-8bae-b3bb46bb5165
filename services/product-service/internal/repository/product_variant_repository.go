package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"pay-mall/services/product-service/internal/db"
	"pay-mall/services/product-service/internal/model"
)

// ProductVariantRepository 商品变体数据访问接口
type ProductVariantRepository interface {
	Create(ctx context.Context, variant *model.ProductVariant) error
	GetByID(ctx context.Context, id string) (*model.ProductVariant, error)
	GetBySKU(ctx context.Context, sku string) (*model.ProductVariant, error)
	GetByProductID(ctx context.Context, productID string) ([]*model.ProductVariant, error)
	Update(ctx context.Context, variant *model.ProductVariant) error
	Delete(ctx context.Context, id string) error
	UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error
	GetAvailable(ctx context.Context, productID string) ([]*model.ProductVariant, error)
	ReserveStock(ctx context.Context, id string, quantity int) error
	ReleaseStock(ctx context.Context, id string, quantity int) error
}

// sqlcProductVariantRepository sqlc实现的商品变体数据访问
type sqlcProductVariantRepository struct {
	db      *sql.DB
	queries *db.Queries
}

// NewProductVariantRepository 创建商品变体仓库实例
func NewProductVariantRepository(database *sql.DB) ProductVariantRepository {
	return &sqlcProductVariantRepository{
		db:      database,
		queries: db.New(database),
	}
}

// Create 创建商品变体
func (r *sqlcProductVariantRepository) Create(ctx context.Context, variant *model.ProductVariant) error {
	var sku sql.NullString
	if variant.SKU != "" {
		sku = sql.NullString{String: variant.SKU, Valid: true}
	}

	var imageURL sql.NullString
	if variant.ImageURL != nil {
		imageURL = sql.NullString{String: *variant.ImageURL, Valid: true}
	}

	var attributes []byte
	if variant.Attributes != nil {
		value, err := variant.Attributes.Value()
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}
		if value != nil {
			attributes = value.([]byte)
		}
	}

	return r.queries.CreateProductVariant(ctx, db.CreateProductVariantParams{
		ID:         variant.ID,
		ProductID:  variant.ProductID,
		Name:       variant.Name,
		Sku:        sku,
		Price:      fmt.Sprintf("%.2f", variant.Price),
		ImageUrl:   imageURL,
		Attributes: attributes,
		Status:     db.ProductVariantsStatus(variant.Status),
		CreatedAt:  variant.CreatedAt,
		UpdatedAt:  variant.UpdatedAt,
	})
}

// GetByID 根据ID获取商品变体
func (r *sqlcProductVariantRepository) GetByID(ctx context.Context, id string) (*model.ProductVariant, error) {
	dbVariant, err := r.queries.GetProductVariantByID(ctx, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product variant not found")
		}
		return nil, err
	}

	return r.dbVariantToModel(dbVariant), nil
}

// GetBySKU 根据SKU获取商品变体
func (r *sqlcProductVariantRepository) GetBySKU(ctx context.Context, sku string) (*model.ProductVariant, error) {
	dbVariant, err := r.queries.GetProductVariantBySKU(ctx, sql.NullString{String: sku, Valid: true})
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product variant not found")
		}
		return nil, err
	}

	return r.dbVariantToModel(dbVariant), nil
}

// GetByProductID 根据商品ID获取所有变体
func (r *sqlcProductVariantRepository) GetByProductID(ctx context.Context, productID string) ([]*model.ProductVariant, error) {
	dbVariants, err := r.queries.ListProductVariants(ctx, productID)
	if err != nil {
		return nil, err
	}

	variants := make([]*model.ProductVariant, len(dbVariants))
	for i, dbVariant := range dbVariants {
		variants[i] = r.dbVariantToModel(dbVariant)
	}

	return variants, nil
}

// Update 更新商品变体
func (r *sqlcProductVariantRepository) Update(ctx context.Context, variant *model.ProductVariant) error {
	var imageURL sql.NullString
	if variant.ImageURL != nil {
		imageURL = sql.NullString{String: *variant.ImageURL, Valid: true}
	}

	var attributes []byte
	if variant.Attributes != nil {
		value, err := variant.Attributes.Value()
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}
		if value != nil {
			attributes = value.([]byte)
		}
	}

	return r.queries.UpdateProductVariant(ctx, db.UpdateProductVariantParams{
		Name:       variant.Name,
		Price:      fmt.Sprintf("%.2f", variant.Price),
		ImageUrl:   imageURL,
		Attributes: attributes,
		UpdatedAt:  time.Now(),
		ID:         variant.ID,
	})
}

// Delete 删除商品变体
func (r *sqlcProductVariantRepository) Delete(ctx context.Context, id string) error {
	return r.queries.DeleteProductVariant(ctx, db.DeleteProductVariantParams{
		UpdatedAt: time.Now(),
		ID:        id,
	})
}

// UpdateStatus 更新商品变体状态
func (r *sqlcProductVariantRepository) UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error {
	return r.queries.UpdateProductVariantStatus(ctx, db.UpdateProductVariantStatusParams{
		Status:    db.ProductVariantsStatus(status),
		UpdatedAt: time.Now(),
		ID:        id,
	})
}

// GetAvailable 获取可用的商品变体
func (r *sqlcProductVariantRepository) GetAvailable(ctx context.Context, productID string) ([]*model.ProductVariant, error) {
	dbVariants, err := r.queries.GetAvailableProductVariants(ctx, productID)
	if err != nil {
		return nil, err
	}

	variants := make([]*model.ProductVariant, len(dbVariants))
	for i, dbVariant := range dbVariants {
		variants[i] = r.dbVariantToModel(dbVariant)
	}

	return variants, nil
}

// ReserveStock 预留库存
func (r *sqlcProductVariantRepository) ReserveStock(ctx context.Context, id string, quantity int) error {
	return r.queries.ReserveProductVariantStock(ctx, db.ReserveProductVariantStockParams{
		ReservedQuantity: int32(quantity),
		UpdatedAt:        time.Now(),
		ID:               id,
	})
}

// ReleaseStock 释放库存
func (r *sqlcProductVariantRepository) ReleaseStock(ctx context.Context, id string, quantity int) error {
	return r.queries.ReleaseProductVariantStock(ctx, db.ReleaseProductVariantStockParams{
		ReservedQuantity: int32(quantity),
		UpdatedAt:        time.Now(),
		ID:               id,
	})
}

// dbVariantToModel 将数据库商品变体转换为模型商品变体
func (r *sqlcProductVariantRepository) dbVariantToModel(dbVariant *db.ProductVariant) *model.ProductVariant {
	variant := &model.ProductVariant{
		ID:        dbVariant.ID,
		ProductID: dbVariant.ProductID,
		Name:      dbVariant.Name,
		SKU:       "",
		Status:    model.ProductStatus(dbVariant.Status),
		CreatedAt: dbVariant.CreatedAt,
		UpdatedAt: dbVariant.UpdatedAt,
	}

	// 解析价格
	if price, err := strconv.ParseFloat(dbVariant.Price, 64); err == nil {
		variant.Price = price
	}

	// 处理SKU
	if dbVariant.Sku.Valid {
		variant.SKU = dbVariant.Sku.String
	}

	// 处理图片URL
	if dbVariant.ImageUrl.Valid {
		variant.ImageURL = &dbVariant.ImageUrl.String
	}

	// 处理属性
	if len(dbVariant.Attributes) > 0 {
		attributes := make(model.JSONMap)
		if err := attributes.Scan(dbVariant.Attributes); err == nil {
			variant.Attributes = attributes
		}
	}

	return variant
}
