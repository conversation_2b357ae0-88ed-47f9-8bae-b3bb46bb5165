package repository

import "errors"

// Repository 层错误定义
var (
	// ErrInvalidInput 无效输入错误
	ErrInvalidInput = errors.New("invalid input")
	
	// ErrNotFound 资源未找到错误
	ErrNotFound = errors.New("resource not found")
	
	// ErrHasChildren 有子资源错误
	ErrHasChildren = errors.New("resource has children")
	
	// ErrDuplicateKey 重复键错误
	ErrDuplicateKey = errors.New("duplicate key")
	
	// ErrInsufficientStock 库存不足错误
	ErrInsufficientStock = errors.New("insufficient stock")
	
	// ErrConcurrentUpdate 并发更新错误
	ErrConcurrentUpdate = errors.New("concurrent update detected")
)
