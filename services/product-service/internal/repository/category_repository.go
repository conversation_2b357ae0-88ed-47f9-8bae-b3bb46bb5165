package repository

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"pay-mall/services/product-service/internal/db"
	"pay-mall/services/product-service/internal/model"
)

// CategoryRepository 分类数据访问接口
type CategoryRepository interface {
	Create(ctx context.Context, category *model.Category) error
	GetByID(ctx context.Context, id string) (*model.Category, error)
	Update(ctx context.Context, category *model.Category) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, req *model.CategoryListRequest) ([]*model.Category, int, error)
	GetTree(ctx context.Context) ([]*model.CategoryTree, error)
	GetChildren(ctx context.Context, parentID string) ([]*model.Category, error)
	GetPath(ctx context.Context, id string) ([]*model.Category, error)
}

// sqlcCategoryRepository sqlc实现的分类数据访问
type sqlcCategoryRepository struct {
	db      *sql.DB
	queries *db.Queries
}

// NewCategoryRepository 创建分类仓库实例
func NewCategoryRepository(database *sql.DB) CategoryRepository {
	return &sqlcCategoryRepository{
		db:      database,
		queries: db.New(database),
	}
}

// Create 创建分类
func (r *sqlcCategoryRepository) Create(ctx context.Context, category *model.Category) error {
	var parentID sql.NullString
	if category.ParentID != nil {
		parentID = sql.NullString{String: *category.ParentID, Valid: true}
	}

	var iconURL sql.NullString
	if category.IconURL != nil {
		iconURL = sql.NullString{String: *category.IconURL, Valid: true}
	}

	var path sql.NullString
	if category.Path != nil {
		path = sql.NullString{String: *category.Path, Valid: true}
	}

	var description sql.NullString
	if category.Description != nil {
		description = sql.NullString{String: *category.Description, Valid: true}
	}

	return r.queries.CreateCategory(ctx, db.CreateCategoryParams{
		ID:          category.ID,
		Name:        category.Name,
		Description: description,
		ParentID:    parentID,
		Level:       int32(category.Level),
		Path:        path,
		IconUrl:     iconURL,
		SortOrder:   int32(category.SortOrder),
		IsActive:    category.IsActive,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	})
}

// GetByID 根据ID获取分类
func (r *sqlcCategoryRepository) GetByID(ctx context.Context, id string) (*model.Category, error) {
	dbCategory, err := r.queries.GetCategoryByID(ctx, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("category not found")
		}
		return nil, err
	}

	return r.dbCategoryToModel(dbCategory), nil
}

// Update 更新分类
func (r *sqlcCategoryRepository) Update(ctx context.Context, category *model.Category) error {
	var description sql.NullString
	if category.Description != nil {
		description = sql.NullString{String: *category.Description, Valid: true}
	}

	return r.queries.UpdateCategory(ctx, db.UpdateCategoryParams{
		Name:        category.Name,
		Description: description,
		SortOrder:   int32(category.SortOrder),
		IsActive:    category.IsActive,
		UpdatedAt:   time.Now(),
		ID:          category.ID,
	})
}

// Delete 删除分类
func (r *sqlcCategoryRepository) Delete(ctx context.Context, id string) error {
	return r.queries.DeleteCategory(ctx, db.DeleteCategoryParams{
		UpdatedAt: time.Now(),
		ID:        id,
	})
}

// List 获取分类列表
func (r *sqlcCategoryRepository) List(ctx context.Context, req *model.CategoryListRequest) ([]*model.Category, int, error) {
	// 获取总数
	total, err := r.queries.CountCategories(ctx)
	if err != nil {
		return nil, 0, err
	}

	// 获取分类列表
	offset := (req.Page - 1) * req.PageSize
	dbCategories, err := r.queries.ListCategories(ctx, db.ListCategoriesParams{
		Limit:  int32(req.PageSize),
		Offset: int32(offset),
	})
	if err != nil {
		return nil, 0, err
	}

	categories := make([]*model.Category, len(dbCategories))
	for i, dbCategory := range dbCategories {
		categories[i] = r.dbCategoryToModel(dbCategory)
	}

	return categories, int(total), nil
}

// GetTree 获取分类树
func (r *sqlcCategoryRepository) GetTree(ctx context.Context) ([]*model.CategoryTree, error) {
	dbCategories, err := r.queries.GetCategoryTree(ctx)
	if err != nil {
		return nil, err
	}

	// 构建分类树
	categoryMap := make(map[string]*model.CategoryTree)
	var roots []*model.CategoryTree

	// 第一遍：创建所有节点
	for _, dbCategory := range dbCategories {
		category := r.dbCategoryToModel(dbCategory)
		tree := &model.CategoryTree{
			Category: category,
			Children: []*model.CategoryTree{},
		}
		categoryMap[category.ID] = tree

		if category.ParentID == nil {
			roots = append(roots, tree)
		}
	}

	// 第二遍：建立父子关系
	for _, dbCategory := range dbCategories {
		category := r.dbCategoryToModel(dbCategory)
		if category.ParentID != nil {
			if parent, exists := categoryMap[*category.ParentID]; exists {
				parent.Children = append(parent.Children, categoryMap[category.ID])
			}
		}
	}

	return roots, nil
}

// GetChildren 获取子分类
func (r *sqlcCategoryRepository) GetChildren(ctx context.Context, parentID string) ([]*model.Category, error) {
	dbCategories, err := r.queries.GetCategoryChildren(ctx, sql.NullString{
		String: parentID,
		Valid:  true,
	})
	if err != nil {
		return nil, err
	}

	categories := make([]*model.Category, len(dbCategories))
	for i, dbCategory := range dbCategories {
		categories[i] = r.dbCategoryToModel(dbCategory)
	}

	return categories, nil
}

// GetPath 获取分类路径
func (r *sqlcCategoryRepository) GetPath(ctx context.Context, id string) ([]*model.Category, error) {
	dbCategories, err := r.queries.GetCategoryPath(ctx, id)
	if err != nil {
		return nil, err
	}

	categories := make([]*model.Category, len(dbCategories))
	for i, dbCategory := range dbCategories {
		categories[i] = r.dbCategoryToModel(dbCategory)
	}

	return categories, nil
}

// dbCategoryToModel 将数据库分类转换为模型分类
func (r *sqlcCategoryRepository) dbCategoryToModel(dbCategory *db.Category) *model.Category {
	category := &model.Category{
		ID:        dbCategory.ID,
		Name:      dbCategory.Name,
		Level:     int(dbCategory.Level),
		SortOrder: int(dbCategory.SortOrder),
		IsActive:  dbCategory.IsActive,
		CreatedAt: dbCategory.CreatedAt,
		UpdatedAt: dbCategory.UpdatedAt,
	}

	if dbCategory.Description.Valid {
		category.Description = &dbCategory.Description.String
	}

	if dbCategory.ParentID.Valid {
		category.ParentID = &dbCategory.ParentID.String
	}

	if dbCategory.Path.Valid {
		category.Path = &dbCategory.Path.String
	}

	if dbCategory.IconUrl.Valid {
		category.IconURL = &dbCategory.IconUrl.String
	}

	return category
}
