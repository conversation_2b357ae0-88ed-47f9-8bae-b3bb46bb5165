package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/repository"
)

// ProductService 商品服务接口
type ProductService interface {
	Create(ctx context.Context, req *model.ProductCreateRequest) (*model.Product, error)
	GetByID(ctx context.Context, id string) (*model.Product, error)
	GetBySKU(ctx context.Context, sku string) (*model.Product, error)
	Update(ctx context.Context, id string, req *model.ProductUpdateRequest) (*model.Product, error)
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, req *model.ProductListRequest) (*model.ProductListResponse, error)
	Search(ctx context.Context, req *model.ProductSearchRequest) (*model.ProductSearchResponse, error)
	UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error
	IncrementViewCount(ctx context.Context, id string) error
	GetPopular(ctx context.Context, limit int) ([]*model.Product, error)
	GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error)
}

// productService 商品服务实现
type productService struct {
	productRepo        repository.ProductRepository
	productVariantRepo repository.ProductVariantRepository
	categoryRepo       repository.CategoryRepository
	eventService       EventService
}

// NewProductService 创建商品服务实例
func NewProductService(
	productRepo repository.ProductRepository,
	productVariantRepo repository.ProductVariantRepository,
	categoryRepo repository.CategoryRepository,
	eventService EventService,
) ProductService {
	return &productService{
		productRepo:        productRepo,
		productVariantRepo: productVariantRepo,
		categoryRepo:       categoryRepo,
		eventService:       eventService,
	}
}

// Create 创建商品
func (s *productService) Create(ctx context.Context, req *model.ProductCreateRequest) (*model.Product, error) {
	// 验证分类是否存在
	_, err := s.categoryRepo.GetByID(ctx, req.CategoryID)
	if err != nil {
		return nil, fmt.Errorf("category not found: %w", err)
	}

	// 验证SKU是否重复
	if req.SKU != nil {
		_, err = s.productRepo.GetBySKU(ctx, *req.SKU)
		if err == nil {
			return nil, fmt.Errorf("SKU already exists: %s", *req.SKU)
		}
	}

	// 创建商品
	status := model.ProductStatusDraft
	if req.Status != nil {
		status = *req.Status
	}

	product := &model.Product{
		ID:            uuid.New().String(),
		Name:          req.Name,
		Description:   req.Description,
		Price:         req.Price,
		OriginalPrice: req.OriginalPrice,
		ImageURL:      req.ImageURL,
		ImageURLs:     req.ImageURLs,
		CategoryID:    req.CategoryID,
		Brand:         req.Brand,
		SKU:           req.SKU,
		Weight:        req.Weight,
		Dimensions:    req.Dimensions,
		Tags:          req.Tags,
		Attributes:    req.Attributes,
		Status:        status,
		SortOrder:     0,
		ViewCount:     0,
		SaleCount:     0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if req.SortOrder != nil {
		product.SortOrder = *req.SortOrder
	}

	// 保存到数据库
	err = s.productRepo.Create(ctx, product)
	if err != nil {
		return nil, fmt.Errorf("failed to create product: %w", err)
	}

	// 发布事件
	_ = s.eventService.PublishProductCreated(product)

	return product, nil
}

// GetByID 根据ID获取商品
func (s *productService) GetByID(ctx context.Context, id string) (*model.Product, error) {
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	return product, nil
}

// GetBySKU 根据SKU获取商品
func (s *productService) GetBySKU(ctx context.Context, sku string) (*model.Product, error) {
	product, err := s.productRepo.GetBySKU(ctx, sku)
	if err != nil {
		return nil, fmt.Errorf("failed to get product by SKU: %w", err)
	}

	return product, nil
}

// Update 更新商品
func (s *productService) Update(ctx context.Context, id string, req *model.ProductUpdateRequest) (*model.Product, error) {
	// 获取现有商品
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("product not found: %w", err)
	}

	// 记录变更
	changes := make(map[string]interface{})

	// 更新字段
	if req.Name != nil && *req.Name != product.Name {
		changes["name"] = map[string]interface{}{"old": product.Name, "new": *req.Name}
		product.Name = *req.Name
	}
	if req.Description != nil {
		changes["description"] = map[string]interface{}{"old": product.Description, "new": req.Description}
		product.Description = req.Description
	}
	if req.Price != nil && *req.Price != product.Price {
		changes["price"] = map[string]interface{}{"old": product.Price, "new": *req.Price}
		product.Price = *req.Price
	}
	if req.CategoryID != nil && *req.CategoryID != product.CategoryID {
		// 验证新分类是否存在
		_, err := s.categoryRepo.GetByID(ctx, *req.CategoryID)
		if err != nil {
			return nil, fmt.Errorf("category not found: %w", err)
		}
		changes["category_id"] = map[string]interface{}{"old": product.CategoryID, "new": *req.CategoryID}
		product.CategoryID = *req.CategoryID
	}

	product.UpdatedAt = time.Now()

	// 保存到数据库
	err = s.productRepo.Update(ctx, product)
	if err != nil {
		return nil, fmt.Errorf("failed to update product: %w", err)
	}

	// 发布事件
	if len(changes) > 0 {
		_ = s.eventService.PublishProductUpdated(id, changes)
	}

	return product, nil
}

// Delete 删除商品
func (s *productService) Delete(ctx context.Context, id string) error {
	// 检查商品是否存在
	_, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("product not found: %w", err)
	}

	// 删除商品
	err = s.productRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete product: %w", err)
	}

	// 发布事件
	_ = s.eventService.PublishProductDeleted(id)

	return nil
}

// List 获取商品列表
func (s *productService) List(ctx context.Context, req *model.ProductListRequest) (*model.ProductListResponse, error) {
	products, total, err := s.productRepo.List(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to list products: %w", err)
	}

	return &model.ProductListResponse{
		Products: products,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// Search 搜索商品
func (s *productService) Search(ctx context.Context, req *model.ProductSearchRequest) (*model.ProductSearchResponse, error) {
	products, total, err := s.productRepo.Search(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to search products: %w", err)
	}

	return &model.ProductSearchResponse{
		Products: products,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Query:    req.Query,
	}, nil
}

// UpdateStatus 更新商品状态
func (s *productService) UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error {
	// 获取现有商品
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("product not found: %w", err)
	}

	oldStatus := product.Status
	product.Status = status
	product.UpdatedAt = time.Now()

	// 保存到数据库
	err = s.productRepo.Update(ctx, product)
	if err != nil {
		return fmt.Errorf("failed to update product status: %w", err)
	}

	// 发布事件
	_ = s.eventService.PublishProductStatusChanged(id, oldStatus, status)

	return nil
}

// GetPopular 获取热门商品
func (s *productService) GetPopular(ctx context.Context, limit int) ([]*model.Product, error) {
	products, err := s.productRepo.GetPopular(ctx, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular products: %w", err)
	}

	return products, nil
}

// GetByCategory 根据分类获取商品
func (s *productService) GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error) {
	products, err := s.productRepo.GetByCategory(ctx, categoryID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get products by category: %w", err)
	}

	return products, nil
}

// IncrementViewCount 增加商品浏览次数
func (s *productService) IncrementViewCount(ctx context.Context, id string) error {
	err := s.productRepo.IncrementViewCount(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to increment view count: %w", err)
	}

	return nil
}
