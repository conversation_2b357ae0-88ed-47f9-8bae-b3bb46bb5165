package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/repository"
)

// CategoryService 分类服务接口
type CategoryService interface {
	Create(ctx context.Context, req *model.CategoryCreateRequest) (*model.Category, error)
	GetByID(ctx context.Context, id string) (*model.Category, error)
	Update(ctx context.Context, id string, req *model.CategoryUpdateRequest) (*model.Category, error)
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, req *model.CategoryListRequest) (*model.CategoryListResponse, error)
	GetTree(ctx context.Context) ([]*model.CategoryTree, error)
	GetChildren(ctx context.Context, parentID string) ([]*model.Category, error)
	GetPath(ctx context.Context, id string) ([]*model.Category, error)
	UpdateStatus(ctx context.Context, id string, isActive bool) error
}

// categoryService 分类服务实现
type categoryService struct {
	categoryRepo repository.CategoryRepository
	eventService EventService
}

// NewCategoryService 创建分类服务实例
func NewCategoryService(categoryRepo repository.CategoryRepository, eventService EventService) CategoryService {
	return &categoryService{
		categoryRepo: categoryRepo,
		eventService: eventService,
	}
}

// Create 创建分类
func (s *categoryService) Create(ctx context.Context, req *model.CategoryCreateRequest) (*model.Category, error) {
	// 验证父分类是否存在
	var level int = 1
	if req.ParentID != nil {
		parent, err := s.categoryRepo.GetByID(ctx, *req.ParentID)
		if err != nil {
			return nil, fmt.Errorf("parent category not found: %w", err)
		}
		level = parent.Level + 1
	}

	// 创建分类
	category := &model.Category{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		ParentID:    req.ParentID,
		Level:       level,
		SortOrder:   0,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}

	// 保存到数据库
	err := s.categoryRepo.Create(ctx, category)
	if err != nil {
		return nil, fmt.Errorf("failed to create category: %w", err)
	}

	// 发布事件
	_ = s.eventService.PublishCategoryCreated(category)

	return category, nil
}

// GetByID 根据ID获取分类
func (s *categoryService) GetByID(ctx context.Context, id string) (*model.Category, error) {
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	return category, nil
}

// Update 更新分类
func (s *categoryService) Update(ctx context.Context, id string, req *model.CategoryUpdateRequest) (*model.Category, error) {
	// 获取现有分类
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("category not found: %w", err)
	}

	// 记录变更
	changes := make(map[string]interface{})

	// 更新字段
	if req.Name != nil && *req.Name != category.Name {
		changes["name"] = map[string]interface{}{"old": category.Name, "new": *req.Name}
		category.Name = *req.Name
	}
	if req.Description != nil {
		changes["description"] = map[string]interface{}{"old": category.Description, "new": req.Description}
		category.Description = req.Description
	}
	if req.SortOrder != nil && *req.SortOrder != category.SortOrder {
		changes["sort_order"] = map[string]interface{}{"old": category.SortOrder, "new": *req.SortOrder}
		category.SortOrder = *req.SortOrder
	}
	if req.IsActive != nil && *req.IsActive != category.IsActive {
		changes["is_active"] = map[string]interface{}{"old": category.IsActive, "new": *req.IsActive}
		category.IsActive = *req.IsActive
	}

	category.UpdatedAt = time.Now()

	// 保存到数据库
	err = s.categoryRepo.Update(ctx, category)
	if err != nil {
		return nil, fmt.Errorf("failed to update category: %w", err)
	}

	// 发布事件
	if len(changes) > 0 {
		_ = s.eventService.PublishCategoryUpdated(id, changes)
	}

	return category, nil
}

// Delete 删除分类
func (s *categoryService) Delete(ctx context.Context, id string) error {
	// 检查分类是否存在
	_, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("category not found: %w", err)
	}

	// 检查是否有子分类
	children, err := s.categoryRepo.GetChildren(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check children: %w", err)
	}
	if len(children) > 0 {
		return fmt.Errorf("cannot delete category with children")
	}

	// 删除分类
	err = s.categoryRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete category: %w", err)
	}

	// 发布事件
	_ = s.eventService.PublishCategoryDeleted(id)

	return nil
}

// List 获取分类列表
func (s *categoryService) List(ctx context.Context, req *model.CategoryListRequest) (*model.CategoryListResponse, error) {
	categories, total, err := s.categoryRepo.List(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to list categories: %w", err)
	}

	return &model.CategoryListResponse{
		Categories: categories,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
	}, nil
}

// GetTree 获取分类树
func (s *categoryService) GetTree(ctx context.Context) ([]*model.CategoryTree, error) {
	tree, err := s.categoryRepo.GetTree(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get category tree: %w", err)
	}

	return tree, nil
}

// GetChildren 获取子分类
func (s *categoryService) GetChildren(ctx context.Context, parentID string) ([]*model.Category, error) {
	children, err := s.categoryRepo.GetChildren(ctx, parentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get children: %w", err)
	}

	return children, nil
}

// GetPath 获取分类路径
func (s *categoryService) GetPath(ctx context.Context, id string) ([]*model.Category, error) {
	path, err := s.categoryRepo.GetPath(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get category path: %w", err)
	}

	return path, nil
}

// UpdateStatus 更新分类状态
func (s *categoryService) UpdateStatus(ctx context.Context, id string, isActive bool) error {
	// 获取现有分类
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get category: %w", err)
	}

	// 更新状态
	category.IsActive = isActive
	category.UpdatedAt = time.Now()

	// 保存到数据库
	err = s.categoryRepo.Update(ctx, category)
	if err != nil {
		return fmt.Errorf("failed to update category status: %w", err)
	}

	return nil
}
