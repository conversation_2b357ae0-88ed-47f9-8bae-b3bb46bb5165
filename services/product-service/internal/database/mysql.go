package database

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"

	"pay-mall/services/product-service/internal/config"
)

// Database 数据库连接管理
type Database struct {
	MySQL *sql.DB
}

// NewDatabase 创建数据库连接
func NewDatabase(cfg *config.Config) (*Database, error) {
	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
	)

	// 初始化MySQL连接
	mysql, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	// 配置连接池
	mysql.SetMaxOpenConns(100)
	mysql.SetMaxIdleConns(10)
	mysql.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := mysql.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping MySQL: %w", err)
	}

	return &Database{
		MySQL: mysql,
	}, nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	if d.MySQL != nil {
		return d.MySQL.Close()
	}
	return nil
}
