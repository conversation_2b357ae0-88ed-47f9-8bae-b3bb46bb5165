package event

import (
	"encoding/json"
	"time"

	"pay-mall/services/product-service/internal/model"
)

// EventType 事件类型
type EventType string

const (
	EventTypeProductCreated      EventType = "ProductCreated"
	EventTypeProductUpdated      EventType = "ProductUpdated"
	EventTypeProductDeleted      EventType = "ProductDeleted"
	EventTypeProductStatusChanged EventType = "ProductStatusChanged"
)

// BaseEvent 基础事件结构
type BaseEvent struct {
	ID        string    `json:"id"`
	Type      EventType `json:"type"`
	Source    string    `json:"source"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
}

// ProductCreatedEvent 商品创建事件
type ProductCreatedEvent struct {
	BaseEvent
	Data ProductCreatedData `json:"data"`
}

// ProductCreatedData 商品创建事件数据
type ProductCreatedData struct {
	ProductID   string  `json:"product_id"`
	Name        string  `json:"name"`
	Price       float64 `json:"price"`
	CategoryID  string  `json:"category_id"`
	Brand       *string `json:"brand,omitempty"`
	SKU         *string `json:"sku,omitempty"`
	Status      string  `json:"status"`
	CreatedBy   string  `json:"created_by,omitempty"`
}

// ProductUpdatedEvent 商品更新事件
type ProductUpdatedEvent struct {
	BaseEvent
	Data ProductUpdatedData `json:"data"`
}

// ProductUpdatedData 商品更新事件数据
type ProductUpdatedData struct {
	ProductID   string                 `json:"product_id"`
	Changes     map[string]interface{} `json:"changes"`
	PreviousData map[string]interface{} `json:"previous_data"`
	UpdatedBy   string                 `json:"updated_by,omitempty"`
}

// ProductDeletedEvent 商品删除事件
type ProductDeletedEvent struct {
	BaseEvent
	Data ProductDeletedData `json:"data"`
}

// ProductDeletedData 商品删除事件数据
type ProductDeletedData struct {
	ProductID string `json:"product_id"`
	Name      string `json:"name"`
	SKU       *string `json:"sku,omitempty"`
	DeletedBy string `json:"deleted_by,omitempty"`
}

// ProductStatusChangedEvent 商品状态变更事件
type ProductStatusChangedEvent struct {
	BaseEvent
	Data ProductStatusChangedData `json:"data"`
}

// ProductStatusChangedData 商品状态变更事件数据
type ProductStatusChangedData struct {
	ProductID     string `json:"product_id"`
	PreviousStatus string `json:"previous_status"`
	NewStatus     string `json:"new_status"`
	Reason        string `json:"reason,omitempty"`
	ChangedBy     string `json:"changed_by,omitempty"`
}

// EventPublisher 事件发布器接口
type EventPublisher interface {
	PublishProductCreated(product *model.Product) error
	PublishProductUpdated(productID string, changes, previousData map[string]interface{}) error
	PublishProductDeleted(product *model.Product) error
	PublishProductStatusChanged(productID string, previousStatus, newStatus model.ProductStatus, reason string) error
}

// NewProductCreatedEvent 创建商品创建事件
func NewProductCreatedEvent(product *model.Product) *ProductCreatedEvent {
	return &ProductCreatedEvent{
		BaseEvent: BaseEvent{
			ID:        generateEventID(),
			Type:      EventTypeProductCreated,
			Source:    "product-service",
			Timestamp: time.Now(),
			Version:   "1.0",
		},
		Data: ProductCreatedData{
			ProductID:  product.ID,
			Name:       product.Name,
			Price:      product.Price,
			CategoryID: product.CategoryID,
			Brand:      product.Brand,
			SKU:        product.SKU,
			Status:     string(product.Status),
		},
	}
}

// NewProductUpdatedEvent 创建商品更新事件
func NewProductUpdatedEvent(productID string, changes, previousData map[string]interface{}) *ProductUpdatedEvent {
	return &ProductUpdatedEvent{
		BaseEvent: BaseEvent{
			ID:        generateEventID(),
			Type:      EventTypeProductUpdated,
			Source:    "product-service",
			Timestamp: time.Now(),
			Version:   "1.0",
		},
		Data: ProductUpdatedData{
			ProductID:    productID,
			Changes:      changes,
			PreviousData: previousData,
		},
	}
}

// NewProductDeletedEvent 创建商品删除事件
func NewProductDeletedEvent(product *model.Product) *ProductDeletedEvent {
	return &ProductDeletedEvent{
		BaseEvent: BaseEvent{
			ID:        generateEventID(),
			Type:      EventTypeProductDeleted,
			Source:    "product-service",
			Timestamp: time.Now(),
			Version:   "1.0",
		},
		Data: ProductDeletedData{
			ProductID: product.ID,
			Name:      product.Name,
			SKU:       product.SKU,
		},
	}
}

// NewProductStatusChangedEvent 创建商品状态变更事件
func NewProductStatusChangedEvent(productID string, previousStatus, newStatus model.ProductStatus, reason string) *ProductStatusChangedEvent {
	return &ProductStatusChangedEvent{
		BaseEvent: BaseEvent{
			ID:        generateEventID(),
			Type:      EventTypeProductStatusChanged,
			Source:    "product-service",
			Timestamp: time.Now(),
			Version:   "1.0",
		},
		Data: ProductStatusChangedData{
			ProductID:      productID,
			PreviousStatus: string(previousStatus),
			NewStatus:      string(newStatus),
			Reason:         reason,
		},
	}
}

// ToJSON 将事件转换为JSON
func (e *ProductCreatedEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// ToJSON 将事件转换为JSON
func (e *ProductUpdatedEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// ToJSON 将事件转换为JSON
func (e *ProductDeletedEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// ToJSON 将事件转换为JSON
func (e *ProductStatusChangedEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// generateEventID 生成事件ID
func generateEventID() string {
	// 这里可以使用UUID或其他唯一ID生成方式
	return time.Now().Format("20060102150405") + "-" + "product"
}
