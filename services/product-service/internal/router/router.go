package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"pay-mall/services/product-service/internal/handler"
	"pay-mall/services/product-service/internal/middleware"
)

// Router 路由器
type Router struct {
	categoryHandler *handler.CategoryHandler
	productHandler  *handler.ProductHandler
}

// NewRouter 创建路由器实例
func NewRouter(categoryHandler *handler.CategoryHandler, productHandler *handler.ProductHandler) *Router {
	return &Router{
		categoryHandler: categoryHandler,
		productHandler:  productHandler,
	}
}

// SetupRoutes 设置路由
func (r *Router) SetupRoutes() *gin.Engine {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎
	engine := gin.New()

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())
	engine.Use(middleware.CORS())
	engine.Use(middleware.RequestID())

	// 健康检查
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "product-service",
		})
	})

	// API文档
	engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API路由组
	api := engine.Group("/api/v1")
	{
		// 分类路由
		categories := api.Group("/categories")
		{
			categories.POST("", r.categoryHandler.Create)
			categories.GET("", r.categoryHandler.List)
			categories.GET("/tree", r.categoryHandler.GetTree)
			categories.GET("/:id", r.categoryHandler.GetByID)
			categories.PUT("/:id", r.categoryHandler.Update)
			categories.DELETE("/:id", r.categoryHandler.Delete)
			categories.GET("/:id/children", r.categoryHandler.GetChildren)
			categories.GET("/:id/path", r.categoryHandler.GetPath)
			categories.PATCH("/:id/status", r.categoryHandler.UpdateStatus)
		}

		// 商品路由
		products := api.Group("/products")
		{
			products.POST("", r.productHandler.Create)
			products.GET("", r.productHandler.List)
			products.GET("/search", r.productHandler.Search)
			products.GET("/popular", r.productHandler.GetPopular)
			products.GET("/sku/:sku", r.productHandler.GetBySKU)
			products.GET("/category/:category_id", r.productHandler.GetByCategory)
			products.GET("/:id", r.productHandler.GetByID)
			products.PUT("/:id", r.productHandler.Update)
			products.DELETE("/:id", r.productHandler.Delete)
			products.PATCH("/:id/status", r.productHandler.UpdateStatus)
			products.POST("/:id/view", r.productHandler.IncrementViewCount)
		}
	}

	return engine
}
