// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	
)

type ProductVariantsStatus string

const (
	ProductVariantsStatusACTIVE     ProductVariantsStatus = "ACTIVE"
	ProductVariantsStatusINACTIVE   ProductVariantsStatus = "INACTIVE"
	ProductVariantsStatusOUTOFSTOCK ProductVariantsStatus = "OUT_OF_STOCK"
)

func (e *ProductVariantsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProductVariantsStatus(s)
	case string:
		*e = ProductVariantsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ProductVariantsStatus: %T", src)
	}
	return nil
}

type NullProductVariantsStatus struct {
	ProductVariantsStatus ProductVariantsStatus `json:"product_variants_status"`
	Valid                 bool                  `json:"valid"` // Valid is true if ProductVariantsStatus is not NULL
}

// <PERSON><PERSON> implements the Scanner interface.
func (ns *NullProductVariantsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ProductVariantsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProductVariantsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProductVariantsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProductVariantsStatus), nil
}

func (e ProductVariantsStatus) Valid() bool {
	switch e {
	case ProductVariantsStatusACTIVE,
		ProductVariantsStatusINACTIVE,
		ProductVariantsStatusOUTOFSTOCK:
		return true
	}
	return false
}

func AllProductVariantsStatusValues() []ProductVariantsStatus {
	return []ProductVariantsStatus{
		ProductVariantsStatusACTIVE,
		ProductVariantsStatusINACTIVE,
		ProductVariantsStatusOUTOFSTOCK,
	}
}

type ProductsStatus string

const (
	ProductsStatusDRAFT        ProductsStatus = "DRAFT"
	ProductsStatusACTIVE       ProductsStatus = "ACTIVE"
	ProductsStatusINACTIVE     ProductsStatus = "INACTIVE"
	ProductsStatusOUTOFSTOCK   ProductsStatus = "OUT_OF_STOCK"
	ProductsStatusDISCONTINUED ProductsStatus = "DISCONTINUED"
	ProductsStatusDELETED      ProductsStatus = "DELETED"
)

func (e *ProductsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProductsStatus(s)
	case string:
		*e = ProductsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ProductsStatus: %T", src)
	}
	return nil
}

type NullProductsStatus struct {
	ProductsStatus ProductsStatus `json:"products_status"`
	Valid          bool           `json:"valid"` // Valid is true if ProductsStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProductsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ProductsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProductsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProductsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProductsStatus), nil
}

func (e ProductsStatus) Valid() bool {
	switch e {
	case ProductsStatusDRAFT,
		ProductsStatusACTIVE,
		ProductsStatusINACTIVE,
		ProductsStatusOUTOFSTOCK,
		ProductsStatusDISCONTINUED,
		ProductsStatusDELETED:
		return true
	}
	return false
}

func AllProductsStatusValues() []ProductsStatus {
	return []ProductsStatus{
		ProductsStatusDRAFT,
		ProductsStatusACTIVE,
		ProductsStatusINACTIVE,
		ProductsStatusOUTOFSTOCK,
		ProductsStatusDISCONTINUED,
		ProductsStatusDELETED,
	}
}

type Category struct {
	ID          string         `db:"id" json:"id"`
	Name        string         `db:"name" json:"name"`
	Description sql.NullString `db:"description" json:"description"`
	ParentID    sql.NullString `db:"parent_id" json:"parent_id"`
	Level       int32          `db:"level" json:"level"`
	Path        sql.NullString `db:"path" json:"path"`
	IconUrl     sql.NullString `db:"icon_url" json:"icon_url"`
	SortOrder   int32          `db:"sort_order" json:"sort_order"`
	IsActive    bool           `db:"is_active" json:"is_active"`
	CreatedAt   time.Time      `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time      `db:"updated_at" json:"updated_at"`
}

type Product struct {
	ID            string          `db:"id" json:"id"`
	Name          string          `db:"name" json:"name"`
	Description   sql.NullString  `db:"description" json:"description"`
	Price         string          `db:"price" json:"price"`
	OriginalPrice sql.NullString  `db:"original_price" json:"original_price"`
	ImageUrl      sql.NullString  `db:"image_url" json:"image_url"`
	ImageUrls     json.RawMessage `db:"image_urls" json:"image_urls"`
	CategoryID    string          `db:"category_id" json:"category_id"`
	Brand         sql.NullString  `db:"brand" json:"brand"`
	Sku           sql.NullString  `db:"sku" json:"sku"`
	Weight        sql.NullString  `db:"weight" json:"weight"`
	Dimensions    json.RawMessage `db:"dimensions" json:"dimensions"`
	Tags          json.RawMessage `db:"tags" json:"tags"`
	Attributes    json.RawMessage `db:"attributes" json:"attributes"`
	Status        ProductsStatus  `db:"status" json:"status"`
	SortOrder     int32           `db:"sort_order" json:"sort_order"`
	ViewCount     int32           `db:"view_count" json:"view_count"`
	SaleCount     int32           `db:"sale_count" json:"sale_count"`
	CreatedAt     time.Time       `db:"created_at" json:"created_at"`
	UpdatedAt     time.Time       `db:"updated_at" json:"updated_at"`
}

type ProductVariant struct {
	ID               string                `db:"id" json:"id"`
	ProductID        string                `db:"product_id" json:"product_id"`
	Name             string                `db:"name" json:"name"`
	Sku              sql.NullString        `db:"sku" json:"sku"`
	Price            string                `db:"price" json:"price"`
	OriginalPrice    sql.NullString        `db:"original_price" json:"original_price"`
	StockQuantity    int32                 `db:"stock_quantity" json:"stock_quantity"`
	ReservedQuantity int32                 `db:"reserved_quantity" json:"reserved_quantity"`
	ImageUrl         sql.NullString        `db:"image_url" json:"image_url"`
	Attributes       json.RawMessage       `db:"attributes" json:"attributes"`
	Weight           sql.NullString        `db:"weight" json:"weight"`
	Dimensions       json.RawMessage       `db:"dimensions" json:"dimensions"`
	Status           ProductVariantsStatus `db:"status" json:"status"`
	SortOrder        int32                 `db:"sort_order" json:"sort_order"`
	CreatedAt        time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time             `db:"updated_at" json:"updated_at"`
}
