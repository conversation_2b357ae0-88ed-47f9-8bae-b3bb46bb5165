// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: products.sql

package db

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

)

const CountProducts = `-- name: CountProducts :one
SELECT COUNT(*) FROM products WHERE status != 'DELETED'
`

func (q *Queries) CountProducts(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, CountProducts)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CountProductsByCategory = `-- name: CountProductsByCategory :one
SELECT COUNT(*) FROM products WHERE category_id = ? AND status != 'DELETED'
`

func (q *Queries) CountProductsByCategory(ctx context.Context, categoryID string) (int64, error) {
	row := q.db.QueryRowContext(ctx, CountProductsByCategory, categoryID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CountProductsByStatus = `-- name: CountProductsByStatus :one
SELECT COUNT(*) FROM products WHERE status = ?
`

func (q *Queries) CountProductsByStatus(ctx context.Context, status ProductsStatus) (int64, error) {
	row := q.db.QueryRowContext(ctx, CountProductsByStatus, status)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CountSearchProducts = `-- name: CountSearchProducts :one
SELECT COUNT(*) 
FROM products 
WHERE (name LIKE ? OR description LIKE ? OR brand LIKE ?) 
  AND status != 'DELETED'
`

type CountSearchProductsParams struct {
	Name        string         `db:"name" json:"name"`
	Description sql.NullString `db:"description" json:"description"`
	Brand       sql.NullString `db:"brand" json:"brand"`
}

func (q *Queries) CountSearchProducts(ctx context.Context, arg CountSearchProductsParams) (int64, error) {
	row := q.db.QueryRowContext(ctx, CountSearchProducts, arg.Name, arg.Description, arg.Brand)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CreateProduct = `-- name: CreateProduct :exec
INSERT INTO products (
    id, name, description, price, original_price, image_url, image_urls, 
    category_id, brand, sku, weight, dimensions, tags, attributes, 
    status, sort_order, view_count, sale_count, created_at, updated_at
) VALUES (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
)
`

type CreateProductParams struct {
	ID            string          `db:"id" json:"id"`
	Name          string          `db:"name" json:"name"`
	Description   sql.NullString  `db:"description" json:"description"`
	Price         string          `db:"price" json:"price"`
	OriginalPrice sql.NullString  `db:"original_price" json:"original_price"`
	ImageUrl      sql.NullString  `db:"image_url" json:"image_url"`
	ImageUrls     json.RawMessage `db:"image_urls" json:"image_urls"`
	CategoryID    string          `db:"category_id" json:"category_id"`
	Brand         sql.NullString  `db:"brand" json:"brand"`
	Sku           sql.NullString  `db:"sku" json:"sku"`
	Weight        sql.NullString  `db:"weight" json:"weight"`
	Dimensions    json.RawMessage `db:"dimensions" json:"dimensions"`
	Tags          json.RawMessage `db:"tags" json:"tags"`
	Attributes    json.RawMessage `db:"attributes" json:"attributes"`
	Status        ProductsStatus  `db:"status" json:"status"`
	SortOrder     int32           `db:"sort_order" json:"sort_order"`
	ViewCount     int32           `db:"view_count" json:"view_count"`
	SaleCount     int32           `db:"sale_count" json:"sale_count"`
	CreatedAt     time.Time       `db:"created_at" json:"created_at"`
	UpdatedAt     time.Time       `db:"updated_at" json:"updated_at"`
}

func (q *Queries) CreateProduct(ctx context.Context, arg CreateProductParams) error {
	_, err := q.db.ExecContext(ctx, CreateProduct,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.Price,
		arg.OriginalPrice,
		arg.ImageUrl,
		arg.ImageUrls,
		arg.CategoryID,
		arg.Brand,
		arg.Sku,
		arg.Weight,
		arg.Dimensions,
		arg.Tags,
		arg.Attributes,
		arg.Status,
		arg.SortOrder,
		arg.ViewCount,
		arg.SaleCount,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	return err
}

const DeleteProduct = `-- name: DeleteProduct :exec
UPDATE products 
SET status = 'DELETED', updated_at = ?
WHERE id = ?
`

type DeleteProductParams struct {
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
	ID        string    `db:"id" json:"id"`
}

func (q *Queries) DeleteProduct(ctx context.Context, arg DeleteProductParams) error {
	_, err := q.db.ExecContext(ctx, DeleteProduct, arg.UpdatedAt, arg.ID)
	return err
}

const GetPopularProducts = `-- name: GetPopularProducts :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE status = 'ACTIVE'
ORDER BY sale_count DESC, view_count DESC
LIMIT ?
`

func (q *Queries) GetPopularProducts(ctx context.Context, limit int32) ([]*Product, error) {
	rows, err := q.db.QueryContext(ctx, GetPopularProducts, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.OriginalPrice,
			&i.ImageUrl,
			&i.ImageUrls,
			&i.CategoryID,
			&i.Brand,
			&i.Sku,
			&i.Weight,
			&i.Dimensions,
			&i.Tags,
			&i.Attributes,
			&i.Status,
			&i.SortOrder,
			&i.ViewCount,
			&i.SaleCount,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetProductByID = `-- name: GetProductByID :one
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE id = ? AND status != 'DELETED'
`

func (q *Queries) GetProductByID(ctx context.Context, id string) (*Product, error) {
	row := q.db.QueryRowContext(ctx, GetProductByID, id)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Price,
		&i.OriginalPrice,
		&i.ImageUrl,
		&i.ImageUrls,
		&i.CategoryID,
		&i.Brand,
		&i.Sku,
		&i.Weight,
		&i.Dimensions,
		&i.Tags,
		&i.Attributes,
		&i.Status,
		&i.SortOrder,
		&i.ViewCount,
		&i.SaleCount,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetProductBySKU = `-- name: GetProductBySKU :one
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE sku = ? AND status != 'DELETED'
`

func (q *Queries) GetProductBySKU(ctx context.Context, sku sql.NullString) (*Product, error) {
	row := q.db.QueryRowContext(ctx, GetProductBySKU, sku)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Price,
		&i.OriginalPrice,
		&i.ImageUrl,
		&i.ImageUrls,
		&i.CategoryID,
		&i.Brand,
		&i.Sku,
		&i.Weight,
		&i.Dimensions,
		&i.Tags,
		&i.Attributes,
		&i.Status,
		&i.SortOrder,
		&i.ViewCount,
		&i.SaleCount,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetProductsByCategory = `-- name: GetProductsByCategory :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE category_id = ? AND status = 'ACTIVE'
ORDER BY sort_order ASC, created_at DESC
LIMIT ?
`

type GetProductsByCategoryParams struct {
	CategoryID string `db:"category_id" json:"category_id"`
	Limit      int32  `db:"limit" json:"limit"`
}

func (q *Queries) GetProductsByCategory(ctx context.Context, arg GetProductsByCategoryParams) ([]*Product, error) {
	rows, err := q.db.QueryContext(ctx, GetProductsByCategory, arg.CategoryID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.OriginalPrice,
			&i.ImageUrl,
			&i.ImageUrls,
			&i.CategoryID,
			&i.Brand,
			&i.Sku,
			&i.Weight,
			&i.Dimensions,
			&i.Tags,
			&i.Attributes,
			&i.Status,
			&i.SortOrder,
			&i.ViewCount,
			&i.SaleCount,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const HardDeleteProduct = `-- name: HardDeleteProduct :exec
DELETE FROM products WHERE id = ?
`

func (q *Queries) HardDeleteProduct(ctx context.Context, id string) error {
	_, err := q.db.ExecContext(ctx, HardDeleteProduct, id)
	return err
}

const IncrementSaleCount = `-- name: IncrementSaleCount :exec
UPDATE products 
SET sale_count = sale_count + ?, updated_at = ?
WHERE id = ?
`

type IncrementSaleCountParams struct {
	SaleCount int32     `db:"sale_count" json:"sale_count"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
	ID        string    `db:"id" json:"id"`
}

func (q *Queries) IncrementSaleCount(ctx context.Context, arg IncrementSaleCountParams) error {
	_, err := q.db.ExecContext(ctx, IncrementSaleCount, arg.SaleCount, arg.UpdatedAt, arg.ID)
	return err
}

const IncrementViewCount = `-- name: IncrementViewCount :exec
UPDATE products 
SET view_count = view_count + 1, updated_at = ?
WHERE id = ?
`

type IncrementViewCountParams struct {
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
	ID        string    `db:"id" json:"id"`
}

func (q *Queries) IncrementViewCount(ctx context.Context, arg IncrementViewCountParams) error {
	_, err := q.db.ExecContext(ctx, IncrementViewCount, arg.UpdatedAt, arg.ID)
	return err
}

const ListProducts = `-- name: ListProducts :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE status != 'DELETED'
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?
`

type ListProductsParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

func (q *Queries) ListProducts(ctx context.Context, arg ListProductsParams) ([]*Product, error) {
	rows, err := q.db.QueryContext(ctx, ListProducts, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.OriginalPrice,
			&i.ImageUrl,
			&i.ImageUrls,
			&i.CategoryID,
			&i.Brand,
			&i.Sku,
			&i.Weight,
			&i.Dimensions,
			&i.Tags,
			&i.Attributes,
			&i.Status,
			&i.SortOrder,
			&i.ViewCount,
			&i.SaleCount,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListProductsByCategory = `-- name: ListProductsByCategory :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE category_id = ? AND status != 'DELETED'
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?
`

type ListProductsByCategoryParams struct {
	CategoryID string `db:"category_id" json:"category_id"`
	Limit      int32  `db:"limit" json:"limit"`
	Offset     int32  `db:"offset" json:"offset"`
}

func (q *Queries) ListProductsByCategory(ctx context.Context, arg ListProductsByCategoryParams) ([]*Product, error) {
	rows, err := q.db.QueryContext(ctx, ListProductsByCategory, arg.CategoryID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.OriginalPrice,
			&i.ImageUrl,
			&i.ImageUrls,
			&i.CategoryID,
			&i.Brand,
			&i.Sku,
			&i.Weight,
			&i.Dimensions,
			&i.Tags,
			&i.Attributes,
			&i.Status,
			&i.SortOrder,
			&i.ViewCount,
			&i.SaleCount,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListProductsByStatus = `-- name: ListProductsByStatus :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE status = ?
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?
`

type ListProductsByStatusParams struct {
	Status ProductsStatus `db:"status" json:"status"`
	Limit  int32          `db:"limit" json:"limit"`
	Offset int32          `db:"offset" json:"offset"`
}

func (q *Queries) ListProductsByStatus(ctx context.Context, arg ListProductsByStatusParams) ([]*Product, error) {
	rows, err := q.db.QueryContext(ctx, ListProductsByStatus, arg.Status, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.OriginalPrice,
			&i.ImageUrl,
			&i.ImageUrls,
			&i.CategoryID,
			&i.Brand,
			&i.Sku,
			&i.Weight,
			&i.Dimensions,
			&i.Tags,
			&i.Attributes,
			&i.Status,
			&i.SortOrder,
			&i.ViewCount,
			&i.SaleCount,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchProducts = `-- name: SearchProducts :many
SELECT id, name, description, price, original_price, image_url, image_urls,
       category_id, brand, sku, weight, dimensions, tags, attributes,
       status, sort_order, view_count, sale_count, created_at, updated_at
FROM products 
WHERE (name LIKE ? OR description LIKE ? OR brand LIKE ?) 
  AND status != 'DELETED'
ORDER BY sort_order ASC, created_at DESC
LIMIT ? OFFSET ?
`

type SearchProductsParams struct {
	Name        string         `db:"name" json:"name"`
	Description sql.NullString `db:"description" json:"description"`
	Brand       sql.NullString `db:"brand" json:"brand"`
	Limit       int32          `db:"limit" json:"limit"`
	Offset      int32          `db:"offset" json:"offset"`
}

func (q *Queries) SearchProducts(ctx context.Context, arg SearchProductsParams) ([]*Product, error) {
	rows, err := q.db.QueryContext(ctx, SearchProducts,
		arg.Name,
		arg.Description,
		arg.Brand,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Price,
			&i.OriginalPrice,
			&i.ImageUrl,
			&i.ImageUrls,
			&i.CategoryID,
			&i.Brand,
			&i.Sku,
			&i.Weight,
			&i.Dimensions,
			&i.Tags,
			&i.Attributes,
			&i.Status,
			&i.SortOrder,
			&i.ViewCount,
			&i.SaleCount,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateProduct = `-- name: UpdateProduct :exec
UPDATE products 
SET name = ?, description = ?, price = ?, original_price = ?, 
    image_url = ?, image_urls = ?, category_id = ?, brand = ?, 
    weight = ?, dimensions = ?, tags = ?, attributes = ?, 
    sort_order = ?, updated_at = ?
WHERE id = ?
`

type UpdateProductParams struct {
	Name          string          `db:"name" json:"name"`
	Description   sql.NullString  `db:"description" json:"description"`
	Price         string          `db:"price" json:"price"`
	OriginalPrice sql.NullString  `db:"original_price" json:"original_price"`
	ImageUrl      sql.NullString  `db:"image_url" json:"image_url"`
	ImageUrls     json.RawMessage `db:"image_urls" json:"image_urls"`
	CategoryID    string          `db:"category_id" json:"category_id"`
	Brand         sql.NullString  `db:"brand" json:"brand"`
	Weight        sql.NullString  `db:"weight" json:"weight"`
	Dimensions    json.RawMessage `db:"dimensions" json:"dimensions"`
	Tags          json.RawMessage `db:"tags" json:"tags"`
	Attributes    json.RawMessage `db:"attributes" json:"attributes"`
	SortOrder     int32           `db:"sort_order" json:"sort_order"`
	UpdatedAt     time.Time       `db:"updated_at" json:"updated_at"`
	ID            string          `db:"id" json:"id"`
}

func (q *Queries) UpdateProduct(ctx context.Context, arg UpdateProductParams) error {
	_, err := q.db.ExecContext(ctx, UpdateProduct,
		arg.Name,
		arg.Description,
		arg.Price,
		arg.OriginalPrice,
		arg.ImageUrl,
		arg.ImageUrls,
		arg.CategoryID,
		arg.Brand,
		arg.Weight,
		arg.Dimensions,
		arg.Tags,
		arg.Attributes,
		arg.SortOrder,
		arg.UpdatedAt,
		arg.ID,
	)
	return err
}

const UpdateProductStatus = `-- name: UpdateProductStatus :exec
UPDATE products 
SET status = ?, updated_at = ?
WHERE id = ?
`

type UpdateProductStatusParams struct {
	Status    ProductsStatus `db:"status" json:"status"`
	UpdatedAt time.Time      `db:"updated_at" json:"updated_at"`
	ID        string         `db:"id" json:"id"`
}

func (q *Queries) UpdateProductStatus(ctx context.Context, arg UpdateProductStatusParams) error {
	_, err := q.db.ExecContext(ctx, UpdateProductStatus, arg.Status, arg.UpdatedAt, arg.ID)
	return err
}
