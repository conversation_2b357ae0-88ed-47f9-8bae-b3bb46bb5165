// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: product_variants.sql

package db

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

)

const ConsumeProductVariantStock = `-- name: ConsumeProductVariantStock :exec
UPDATE product_variants 
SET stock_quantity = stock_quantity - ?, 
    reserved_quantity = GREATEST(0, reserved_quantity - ?), 
    updated_at = ?
WHERE id = ?
`

type ConsumeProductVariantStockParams struct {
	StockQuantity    int32     `db:"stock_quantity" json:"stock_quantity"`
	ReservedQuantity int32     `db:"reserved_quantity" json:"reserved_quantity"`
	UpdatedAt        time.Time `db:"updated_at" json:"updated_at"`
	ID               string    `db:"id" json:"id"`
}

func (q *Queries) ConsumeProductVariantStock(ctx context.Context, arg ConsumeProductVariantStockParams) error {
	_, err := q.db.ExecContext(ctx, ConsumeProductVariantStock,
		arg.StockQuantity,
		arg.ReservedQuantity,
		arg.UpdatedAt,
		arg.ID,
	)
	return err
}

const CountProductVariants = `-- name: CountProductVariants :one
SELECT COUNT(*) FROM product_variants WHERE product_id = ? AND status != 'INACTIVE'
`

func (q *Queries) CountProductVariants(ctx context.Context, productID string) (int64, error) {
	row := q.db.QueryRowContext(ctx, CountProductVariants, productID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CreateProductVariant = `-- name: CreateProductVariant :exec
INSERT INTO product_variants (
    id, product_id, name, sku, price, original_price, stock_quantity, 
    reserved_quantity, image_url, attributes, weight, dimensions, 
    status, sort_order, created_at, updated_at
) VALUES (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
)
`

type CreateProductVariantParams struct {
	ID               string                `db:"id" json:"id"`
	ProductID        string                `db:"product_id" json:"product_id"`
	Name             string                `db:"name" json:"name"`
	Sku              sql.NullString        `db:"sku" json:"sku"`
	Price            string                `db:"price" json:"price"`
	OriginalPrice    sql.NullString        `db:"original_price" json:"original_price"`
	StockQuantity    int32                 `db:"stock_quantity" json:"stock_quantity"`
	ReservedQuantity int32                 `db:"reserved_quantity" json:"reserved_quantity"`
	ImageUrl         sql.NullString        `db:"image_url" json:"image_url"`
	Attributes       json.RawMessage       `db:"attributes" json:"attributes"`
	Weight           sql.NullString        `db:"weight" json:"weight"`
	Dimensions       json.RawMessage       `db:"dimensions" json:"dimensions"`
	Status           ProductVariantsStatus `db:"status" json:"status"`
	SortOrder        int32                 `db:"sort_order" json:"sort_order"`
	CreatedAt        time.Time             `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time             `db:"updated_at" json:"updated_at"`
}

func (q *Queries) CreateProductVariant(ctx context.Context, arg CreateProductVariantParams) error {
	_, err := q.db.ExecContext(ctx, CreateProductVariant,
		arg.ID,
		arg.ProductID,
		arg.Name,
		arg.Sku,
		arg.Price,
		arg.OriginalPrice,
		arg.StockQuantity,
		arg.ReservedQuantity,
		arg.ImageUrl,
		arg.Attributes,
		arg.Weight,
		arg.Dimensions,
		arg.Status,
		arg.SortOrder,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	return err
}

const DeleteProductVariant = `-- name: DeleteProductVariant :exec
UPDATE product_variants 
SET status = 'INACTIVE', updated_at = ?
WHERE id = ?
`

type DeleteProductVariantParams struct {
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
	ID        string    `db:"id" json:"id"`
}

func (q *Queries) DeleteProductVariant(ctx context.Context, arg DeleteProductVariantParams) error {
	_, err := q.db.ExecContext(ctx, DeleteProductVariant, arg.UpdatedAt, arg.ID)
	return err
}

const DeleteProductVariantsByProductID = `-- name: DeleteProductVariantsByProductID :exec
UPDATE product_variants 
SET status = 'INACTIVE', updated_at = ?
WHERE product_id = ?
`

type DeleteProductVariantsByProductIDParams struct {
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
	ProductID string    `db:"product_id" json:"product_id"`
}

func (q *Queries) DeleteProductVariantsByProductID(ctx context.Context, arg DeleteProductVariantsByProductIDParams) error {
	_, err := q.db.ExecContext(ctx, DeleteProductVariantsByProductID, arg.UpdatedAt, arg.ProductID)
	return err
}

const GetAvailableProductVariants = `-- name: GetAvailableProductVariants :many
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE product_id = ? AND status = 'ACTIVE' AND (stock_quantity - reserved_quantity) > 0
ORDER BY sort_order ASC, created_at ASC
`

func (q *Queries) GetAvailableProductVariants(ctx context.Context, productID string) ([]*ProductVariant, error) {
	rows, err := q.db.QueryContext(ctx, GetAvailableProductVariants, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ProductVariant{}
	for rows.Next() {
		var i ProductVariant
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.Name,
			&i.Sku,
			&i.Price,
			&i.OriginalPrice,
			&i.StockQuantity,
			&i.ReservedQuantity,
			&i.ImageUrl,
			&i.Attributes,
			&i.Weight,
			&i.Dimensions,
			&i.Status,
			&i.SortOrder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetProductVariantByID = `-- name: GetProductVariantByID :one
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE id = ? AND status != 'INACTIVE'
`

func (q *Queries) GetProductVariantByID(ctx context.Context, id string) (*ProductVariant, error) {
	row := q.db.QueryRowContext(ctx, GetProductVariantByID, id)
	var i ProductVariant
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.Name,
		&i.Sku,
		&i.Price,
		&i.OriginalPrice,
		&i.StockQuantity,
		&i.ReservedQuantity,
		&i.ImageUrl,
		&i.Attributes,
		&i.Weight,
		&i.Dimensions,
		&i.Status,
		&i.SortOrder,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetProductVariantBySKU = `-- name: GetProductVariantBySKU :one
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE sku = ? AND status != 'INACTIVE'
`

func (q *Queries) GetProductVariantBySKU(ctx context.Context, sku sql.NullString) (*ProductVariant, error) {
	row := q.db.QueryRowContext(ctx, GetProductVariantBySKU, sku)
	var i ProductVariant
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.Name,
		&i.Sku,
		&i.Price,
		&i.OriginalPrice,
		&i.StockQuantity,
		&i.ReservedQuantity,
		&i.ImageUrl,
		&i.Attributes,
		&i.Weight,
		&i.Dimensions,
		&i.Status,
		&i.SortOrder,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const HardDeleteProductVariant = `-- name: HardDeleteProductVariant :exec
DELETE FROM product_variants WHERE id = ?
`

func (q *Queries) HardDeleteProductVariant(ctx context.Context, id string) error {
	_, err := q.db.ExecContext(ctx, HardDeleteProductVariant, id)
	return err
}

const ListAllProductVariants = `-- name: ListAllProductVariants :many
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE product_id = ?
ORDER BY sort_order ASC, created_at ASC
`

func (q *Queries) ListAllProductVariants(ctx context.Context, productID string) ([]*ProductVariant, error) {
	rows, err := q.db.QueryContext(ctx, ListAllProductVariants, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ProductVariant{}
	for rows.Next() {
		var i ProductVariant
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.Name,
			&i.Sku,
			&i.Price,
			&i.OriginalPrice,
			&i.StockQuantity,
			&i.ReservedQuantity,
			&i.ImageUrl,
			&i.Attributes,
			&i.Weight,
			&i.Dimensions,
			&i.Status,
			&i.SortOrder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListProductVariants = `-- name: ListProductVariants :many
SELECT id, product_id, name, sku, price, original_price, stock_quantity,
       reserved_quantity, image_url, attributes, weight, dimensions,
       status, sort_order, created_at, updated_at
FROM product_variants 
WHERE product_id = ? AND status != 'INACTIVE'
ORDER BY sort_order ASC, created_at ASC
`

func (q *Queries) ListProductVariants(ctx context.Context, productID string) ([]*ProductVariant, error) {
	rows, err := q.db.QueryContext(ctx, ListProductVariants, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ProductVariant{}
	for rows.Next() {
		var i ProductVariant
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.Name,
			&i.Sku,
			&i.Price,
			&i.OriginalPrice,
			&i.StockQuantity,
			&i.ReservedQuantity,
			&i.ImageUrl,
			&i.Attributes,
			&i.Weight,
			&i.Dimensions,
			&i.Status,
			&i.SortOrder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ReleaseProductVariantStock = `-- name: ReleaseProductVariantStock :exec
UPDATE product_variants 
SET reserved_quantity = GREATEST(0, reserved_quantity - ?), updated_at = ?
WHERE id = ?
`

type ReleaseProductVariantStockParams struct {
	ReservedQuantity int32     `db:"reserved_quantity" json:"reserved_quantity"`
	UpdatedAt        time.Time `db:"updated_at" json:"updated_at"`
	ID               string    `db:"id" json:"id"`
}

func (q *Queries) ReleaseProductVariantStock(ctx context.Context, arg ReleaseProductVariantStockParams) error {
	_, err := q.db.ExecContext(ctx, ReleaseProductVariantStock, arg.ReservedQuantity, arg.UpdatedAt, arg.ID)
	return err
}

const ReserveProductVariantStock = `-- name: ReserveProductVariantStock :exec
UPDATE product_variants 
SET reserved_quantity = reserved_quantity + ?, updated_at = ?
WHERE id = ? AND (stock_quantity - reserved_quantity) >= ?
`

type ReserveProductVariantStockParams struct {
	ReservedQuantity int32     `db:"reserved_quantity" json:"reserved_quantity"`
	UpdatedAt        time.Time `db:"updated_at" json:"updated_at"`
	ID               string    `db:"id" json:"id"`
	StockQuantity    int32     `db:"stock_quantity" json:"stock_quantity"`
}

func (q *Queries) ReserveProductVariantStock(ctx context.Context, arg ReserveProductVariantStockParams) error {
	_, err := q.db.ExecContext(ctx, ReserveProductVariantStock,
		arg.ReservedQuantity,
		arg.UpdatedAt,
		arg.ID,
		arg.StockQuantity,
	)
	return err
}

const UpdateProductVariant = `-- name: UpdateProductVariant :exec
UPDATE product_variants 
SET name = ?, price = ?, original_price = ?, stock_quantity = ?, 
    image_url = ?, attributes = ?, weight = ?, dimensions = ?, 
    sort_order = ?, updated_at = ?
WHERE id = ?
`

type UpdateProductVariantParams struct {
	Name          string          `db:"name" json:"name"`
	Price         string          `db:"price" json:"price"`
	OriginalPrice sql.NullString  `db:"original_price" json:"original_price"`
	StockQuantity int32           `db:"stock_quantity" json:"stock_quantity"`
	ImageUrl      sql.NullString  `db:"image_url" json:"image_url"`
	Attributes    json.RawMessage `db:"attributes" json:"attributes"`
	Weight        sql.NullString  `db:"weight" json:"weight"`
	Dimensions    json.RawMessage `db:"dimensions" json:"dimensions"`
	SortOrder     int32           `db:"sort_order" json:"sort_order"`
	UpdatedAt     time.Time       `db:"updated_at" json:"updated_at"`
	ID            string          `db:"id" json:"id"`
}

func (q *Queries) UpdateProductVariant(ctx context.Context, arg UpdateProductVariantParams) error {
	_, err := q.db.ExecContext(ctx, UpdateProductVariant,
		arg.Name,
		arg.Price,
		arg.OriginalPrice,
		arg.StockQuantity,
		arg.ImageUrl,
		arg.Attributes,
		arg.Weight,
		arg.Dimensions,
		arg.SortOrder,
		arg.UpdatedAt,
		arg.ID,
	)
	return err
}

const UpdateProductVariantStatus = `-- name: UpdateProductVariantStatus :exec
UPDATE product_variants 
SET status = ?, updated_at = ?
WHERE id = ?
`

type UpdateProductVariantStatusParams struct {
	Status    ProductVariantsStatus `db:"status" json:"status"`
	UpdatedAt time.Time             `db:"updated_at" json:"updated_at"`
	ID        string                `db:"id" json:"id"`
}

func (q *Queries) UpdateProductVariantStatus(ctx context.Context, arg UpdateProductVariantStatusParams) error {
	_, err := q.db.ExecContext(ctx, UpdateProductVariantStatus, arg.Status, arg.UpdatedAt, arg.ID)
	return err
}

const UpdateProductVariantStock = `-- name: UpdateProductVariantStock :exec
UPDATE product_variants 
SET stock_quantity = ?, updated_at = ?
WHERE id = ?
`

type UpdateProductVariantStockParams struct {
	StockQuantity int32     `db:"stock_quantity" json:"stock_quantity"`
	UpdatedAt     time.Time `db:"updated_at" json:"updated_at"`
	ID            string    `db:"id" json:"id"`
}

func (q *Queries) UpdateProductVariantStock(ctx context.Context, arg UpdateProductVariantStockParams) error {
	_, err := q.db.ExecContext(ctx, UpdateProductVariantStock, arg.StockQuantity, arg.UpdatedAt, arg.ID)
	return err
}
