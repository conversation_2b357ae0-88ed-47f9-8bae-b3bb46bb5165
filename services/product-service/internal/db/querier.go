// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"context"
	"database/sql"
)

type Querier interface {
	ConsumeProductVariantStock(ctx context.Context, arg ConsumeProductVariantStockParams) error
	CountCategories(ctx context.Context) (int64, error)
	CountProductVariants(ctx context.Context, productID string) (int64, error)
	CountProducts(ctx context.Context) (int64, error)
	CountProductsByCategory(ctx context.Context, categoryID string) (int64, error)
	CountProductsByStatus(ctx context.Context, status ProductsStatus) (int64, error)
	CountSearchProducts(ctx context.Context, arg CountSearchProductsParams) (int64, error)
	CreateCategory(ctx context.Context, arg CreateCategoryParams) error
	CreateProduct(ctx context.Context, arg CreateProductParams) error
	CreateProductVariant(ctx context.Context, arg CreateProductVariantParams) error
	DeleteCategory(ctx context.Context, arg DeleteCategoryParams) error
	DeleteProduct(ctx context.Context, arg DeleteProductParams) error
	DeleteProductVariant(ctx context.Context, arg DeleteProductVariantParams) error
	DeleteProductVariantsByProductID(ctx context.Context, arg DeleteProductVariantsByProductIDParams) error
	GetAvailableProductVariants(ctx context.Context, productID string) ([]*ProductVariant, error)
	GetCategoryByID(ctx context.Context, id string) (*Category, error)
	GetCategoryByIDWithInactive(ctx context.Context, id string) (*Category, error)
	GetCategoryChildren(ctx context.Context, parentID sql.NullString) ([]*Category, error)
	GetCategoryPath(ctx context.Context, id string) ([]*Category, error)
	GetCategoryTree(ctx context.Context) ([]*Category, error)
	GetPopularProducts(ctx context.Context, limit int32) ([]*Product, error)
	GetProductByID(ctx context.Context, id string) (*Product, error)
	GetProductBySKU(ctx context.Context, sku sql.NullString) (*Product, error)
	GetProductVariantByID(ctx context.Context, id string) (*ProductVariant, error)
	GetProductVariantBySKU(ctx context.Context, sku sql.NullString) (*ProductVariant, error)
	GetProductsByCategory(ctx context.Context, arg GetProductsByCategoryParams) ([]*Product, error)
	GetRootCategories(ctx context.Context) ([]*Category, error)
	HardDeleteCategory(ctx context.Context, id string) error
	HardDeleteProduct(ctx context.Context, id string) error
	HardDeleteProductVariant(ctx context.Context, id string) error
	IncrementSaleCount(ctx context.Context, arg IncrementSaleCountParams) error
	IncrementViewCount(ctx context.Context, arg IncrementViewCountParams) error
	ListAllProductVariants(ctx context.Context, productID string) ([]*ProductVariant, error)
	ListCategories(ctx context.Context, arg ListCategoriesParams) ([]*Category, error)
	ListProductVariants(ctx context.Context, productID string) ([]*ProductVariant, error)
	ListProducts(ctx context.Context, arg ListProductsParams) ([]*Product, error)
	ListProductsByCategory(ctx context.Context, arg ListProductsByCategoryParams) ([]*Product, error)
	ListProductsByStatus(ctx context.Context, arg ListProductsByStatusParams) ([]*Product, error)
	ReleaseProductVariantStock(ctx context.Context, arg ReleaseProductVariantStockParams) error
	ReserveProductVariantStock(ctx context.Context, arg ReserveProductVariantStockParams) error
	SearchProducts(ctx context.Context, arg SearchProductsParams) ([]*Product, error)
	UpdateCategory(ctx context.Context, arg UpdateCategoryParams) error
	UpdateProduct(ctx context.Context, arg UpdateProductParams) error
	UpdateProductStatus(ctx context.Context, arg UpdateProductStatusParams) error
	UpdateProductVariant(ctx context.Context, arg UpdateProductVariantParams) error
	UpdateProductVariantStatus(ctx context.Context, arg UpdateProductVariantStatusParams) error
	UpdateProductVariantStock(ctx context.Context, arg UpdateProductVariantStockParams) error
}

var _ Querier = (*Queries)(nil)
