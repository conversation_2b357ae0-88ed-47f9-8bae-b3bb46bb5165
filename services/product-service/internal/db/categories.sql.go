// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: categories.sql

package db

import (
	"context"
	"database/sql"
	"time"
)

const CountCategories = `-- name: CountCategories :one
SELECT COUNT(*) FROM categories WHERE is_active = TRUE
`

func (q *Queries) CountCategories(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, CountCategories)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CreateCategory = `-- name: CreateCategory :exec
INSERT INTO categories (
    id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
) VALUES (
    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
)
`

type CreateCategoryParams struct {
	ID          string         `db:"id" json:"id"`
	Name        string         `db:"name" json:"name"`
	Description sql.NullString `db:"description" json:"description"`
	ParentID    sql.NullString `db:"parent_id" json:"parent_id"`
	Level       int32          `db:"level" json:"level"`
	Path        sql.NullString `db:"path" json:"path"`
	IconUrl     sql.NullString `db:"icon_url" json:"icon_url"`
	SortOrder   int32          `db:"sort_order" json:"sort_order"`
	IsActive    bool           `db:"is_active" json:"is_active"`
	CreatedAt   time.Time      `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time      `db:"updated_at" json:"updated_at"`
}

func (q *Queries) CreateCategory(ctx context.Context, arg CreateCategoryParams) error {
	_, err := q.db.ExecContext(ctx, CreateCategory,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.ParentID,
		arg.Level,
		arg.Path,
		arg.IconUrl,
		arg.SortOrder,
		arg.IsActive,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	return err
}

const DeleteCategory = `-- name: DeleteCategory :exec
UPDATE categories 
SET is_active = FALSE, updated_at = ?
WHERE id = ?
`

type DeleteCategoryParams struct {
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
	ID        string    `db:"id" json:"id"`
}

func (q *Queries) DeleteCategory(ctx context.Context, arg DeleteCategoryParams) error {
	_, err := q.db.ExecContext(ctx, DeleteCategory, arg.UpdatedAt, arg.ID)
	return err
}

const GetCategoryByID = `-- name: GetCategoryByID :one
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE id = ? AND is_active = TRUE
`

func (q *Queries) GetCategoryByID(ctx context.Context, id string) (*Category, error) {
	row := q.db.QueryRowContext(ctx, GetCategoryByID, id)
	var i Category
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.ParentID,
		&i.Level,
		&i.Path,
		&i.IconUrl,
		&i.SortOrder,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetCategoryByIDWithInactive = `-- name: GetCategoryByIDWithInactive :one
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE id = ?
`

func (q *Queries) GetCategoryByIDWithInactive(ctx context.Context, id string) (*Category, error) {
	row := q.db.QueryRowContext(ctx, GetCategoryByIDWithInactive, id)
	var i Category
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.ParentID,
		&i.Level,
		&i.Path,
		&i.IconUrl,
		&i.SortOrder,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetCategoryChildren = `-- name: GetCategoryChildren :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE parent_id = ? AND is_active = TRUE
ORDER BY sort_order ASC, created_at ASC
`

func (q *Queries) GetCategoryChildren(ctx context.Context, parentID sql.NullString) ([]*Category, error) {
	rows, err := q.db.QueryContext(ctx, GetCategoryChildren, parentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Category{}
	for rows.Next() {
		var i Category
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.ParentID,
			&i.Level,
			&i.Path,
			&i.IconUrl,
			&i.SortOrder,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetCategoryPath = `-- name: GetCategoryPath :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories
WHERE id = ?
`

func (q *Queries) GetCategoryPath(ctx context.Context, id string) ([]*Category, error) {
	rows, err := q.db.QueryContext(ctx, GetCategoryPath, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Category{}
	for rows.Next() {
		var i Category
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.ParentID,
			&i.Level,
			&i.Path,
			&i.IconUrl,
			&i.SortOrder,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetCategoryTree = `-- name: GetCategoryTree :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE is_active = TRUE
ORDER BY level ASC, sort_order ASC, created_at ASC
`

func (q *Queries) GetCategoryTree(ctx context.Context) ([]*Category, error) {
	rows, err := q.db.QueryContext(ctx, GetCategoryTree)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Category{}
	for rows.Next() {
		var i Category
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.ParentID,
			&i.Level,
			&i.Path,
			&i.IconUrl,
			&i.SortOrder,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRootCategories = `-- name: GetRootCategories :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE parent_id IS NULL AND is_active = TRUE
ORDER BY sort_order ASC, created_at ASC
`

func (q *Queries) GetRootCategories(ctx context.Context) ([]*Category, error) {
	rows, err := q.db.QueryContext(ctx, GetRootCategories)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Category{}
	for rows.Next() {
		var i Category
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.ParentID,
			&i.Level,
			&i.Path,
			&i.IconUrl,
			&i.SortOrder,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const HardDeleteCategory = `-- name: HardDeleteCategory :exec
DELETE FROM categories WHERE id = ?
`

func (q *Queries) HardDeleteCategory(ctx context.Context, id string) error {
	_, err := q.db.ExecContext(ctx, HardDeleteCategory, id)
	return err
}

const ListCategories = `-- name: ListCategories :many
SELECT id, name, description, parent_id, level, path, icon_url, sort_order, is_active, created_at, updated_at
FROM categories 
WHERE is_active = TRUE
ORDER BY level ASC, sort_order ASC, created_at ASC
LIMIT ? OFFSET ?
`

type ListCategoriesParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

func (q *Queries) ListCategories(ctx context.Context, arg ListCategoriesParams) ([]*Category, error) {
	rows, err := q.db.QueryContext(ctx, ListCategories, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Category{}
	for rows.Next() {
		var i Category
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.ParentID,
			&i.Level,
			&i.Path,
			&i.IconUrl,
			&i.SortOrder,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateCategory = `-- name: UpdateCategory :exec
UPDATE categories 
SET name = ?, description = ?, sort_order = ?, is_active = ?, updated_at = ?
WHERE id = ?
`

type UpdateCategoryParams struct {
	Name        string         `db:"name" json:"name"`
	Description sql.NullString `db:"description" json:"description"`
	SortOrder   int32          `db:"sort_order" json:"sort_order"`
	IsActive    bool           `db:"is_active" json:"is_active"`
	UpdatedAt   time.Time      `db:"updated_at" json:"updated_at"`
	ID          string         `db:"id" json:"id"`
}

func (q *Queries) UpdateCategory(ctx context.Context, arg UpdateCategoryParams) error {
	_, err := q.db.ExecContext(ctx, UpdateCategory,
		arg.Name,
		arg.Description,
		arg.SortOrder,
		arg.IsActive,
		arg.UpdatedAt,
		arg.ID,
	)
	return err
}
