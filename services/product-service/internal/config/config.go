package config

import (
	"pay-mall/pkg/config"
	"time"
)

// Config 商品服务配置，继承通用配置
type Config struct {
	*config.AppConfig
	Cache CacheConfig `mapstructure:"cache"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	ProductTTL  time.Duration `mapstructure:"product_ttl"`
	CategoryTTL time.Duration `mapstructure:"category_ttl"`
	ListTTL     time.Duration `mapstructure:"list_ttl"`
	SearchTTL   time.Duration `mapstructure:"search_ttl"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 使用pkg/config加载通用配置
	appConfig, err := config.LoadConfig(configPath)
	if err != nil {
		return nil, err
	}

	// 创建商品服务配置
	cfg := &Config{
		AppConfig: appConfig,
		Cache: CacheConfig{
			ProductTTL:  time.Hour,
			CategoryTTL: 2 * time.Hour,
			ListTTL:     30 * time.Minute,
			SearchTTL:   15 * time.Minute,
		},
	}

	return cfg, nil
}
