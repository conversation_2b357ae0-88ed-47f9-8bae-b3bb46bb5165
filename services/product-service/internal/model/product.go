package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// ProductStatus 商品状态
type ProductStatus string

const (
	ProductStatusDraft        ProductStatus = "DRAFT"
	ProductStatusActive       ProductStatus = "ACTIVE"
	ProductStatusInactive     ProductStatus = "INACTIVE"
	ProductStatusOutOfStock   ProductStatus = "OUT_OF_STOCK"
	ProductStatusDiscontinued ProductStatus = "DISCONTINUED"
	ProductStatusDeleted      ProductStatus = "DELETED"
)

// JSONMap 用于存储JSON数据的类型
type JSONMap map[string]interface{}

// JSONArray 用于存储JSON数组的类型
type JSONArray []interface{}

// Product 商品模型
type Product struct {
	ID            string        `json:"id" db:"id"`
	Name          string        `json:"name" db:"name"`
	Description   *string       `json:"description" db:"description"`
	Price         float64       `json:"price" db:"price"`
	OriginalPrice *float64      `json:"original_price" db:"original_price"`
	ImageURL      *string       `json:"image_url" db:"image_url"`
	ImageURLs     *JSONArray    `json:"image_urls" db:"image_urls"`
	CategoryID    string        `json:"category_id" db:"category_id"`
	Brand         *string       `json:"brand" db:"brand"`
	SKU           *string       `json:"sku" db:"sku"`
	Weight        *float64      `json:"weight" db:"weight"`
	Dimensions    *JSONMap      `json:"dimensions" db:"dimensions"`
	Tags          *JSONArray    `json:"tags" db:"tags"`
	Attributes    *JSONMap      `json:"attributes" db:"attributes"`
	Status        ProductStatus `json:"status" db:"status"`
	SortOrder     int           `json:"sort_order" db:"sort_order"`
	ViewCount     int           `json:"view_count" db:"view_count"`
	SaleCount     int           `json:"sale_count" db:"sale_count"`
	CreatedAt     time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time     `json:"updated_at" db:"updated_at"`

	// 关联字段（不存储在数据库中）
	Category *Category         `json:"category,omitempty" db:"-"`
	Variants []*ProductVariant `json:"variants,omitempty" db:"-"`
}

// ProductVariant 商品变体模型
type ProductVariant struct {
	ID         string        `json:"id" db:"id"`
	ProductID  string        `json:"product_id" db:"product_id"`
	SKU        string        `json:"sku" db:"sku"`
	Name       string        `json:"name" db:"name"`
	Price      float64       `json:"price" db:"price"`
	Attributes JSONMap       `json:"attributes" db:"attributes"`
	ImageURL   *string       `json:"image_url" db:"image_url"`
	Status     ProductStatus `json:"status" db:"status"`
	CreatedAt  time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time     `json:"updated_at" db:"updated_at"`

	// 关联字段
	Product *Product `json:"product,omitempty" db:"-"`
}

// CreateProductRequest 创建商品请求
type CreateProductRequest struct {
	Name          string                        `json:"name" binding:"required,max=255"`
	Description   *string                       `json:"description" binding:"omitempty,max=2000"`
	Price         float64                       `json:"price" binding:"required,min=0"`
	OriginalPrice *float64                      `json:"original_price" binding:"omitempty,min=0"`
	ImageURL      *string                       `json:"image_url" binding:"omitempty,url"`
	ImageURLs     *JSONArray                    `json:"image_urls" binding:"omitempty"`
	CategoryID    string                        `json:"category_id" binding:"required,uuid"`
	Brand         *string                       `json:"brand" binding:"omitempty,max=100"`
	SKU           *string                       `json:"sku" binding:"omitempty,max=100"`
	Weight        *float64                      `json:"weight" binding:"omitempty,min=0"`
	Dimensions    *JSONMap                      `json:"dimensions" binding:"omitempty"`
	Tags          *JSONArray                    `json:"tags" binding:"omitempty"`
	Attributes    *JSONMap                      `json:"attributes" binding:"omitempty"`
	SortOrder     *int                          `json:"sort_order" binding:"omitempty,min=0"`
	Variants      []CreateProductVariantRequest `json:"variants" binding:"omitempty"`
}

// UpdateProductRequest 更新商品请求
type UpdateProductRequest struct {
	Name          *string        `json:"name" binding:"omitempty,max=255"`
	Description   *string        `json:"description" binding:"omitempty,max=2000"`
	Price         *float64       `json:"price" binding:"omitempty,min=0"`
	OriginalPrice *float64       `json:"original_price" binding:"omitempty,min=0"`
	ImageURL      *string        `json:"image_url" binding:"omitempty,url"`
	ImageURLs     *JSONArray     `json:"image_urls" binding:"omitempty"`
	CategoryID    *string        `json:"category_id" binding:"omitempty,uuid"`
	Brand         *string        `json:"brand" binding:"omitempty,max=100"`
	SKU           *string        `json:"sku" binding:"omitempty,max=100"`
	Weight        *float64       `json:"weight" binding:"omitempty,min=0"`
	Dimensions    *JSONMap       `json:"dimensions" binding:"omitempty"`
	Tags          *JSONArray     `json:"tags" binding:"omitempty"`
	Attributes    *JSONMap       `json:"attributes" binding:"omitempty"`
	Status        *ProductStatus `json:"status" binding:"omitempty,oneof=DRAFT ACTIVE INACTIVE OUT_OF_STOCK DISCONTINUED DELETED"`
	SortOrder     *int           `json:"sort_order" binding:"omitempty,min=0"`
}

// CreateProductVariantRequest 创建商品变体请求
type CreateProductVariantRequest struct {
	SKU        string  `json:"sku" binding:"required,max=100"`
	Name       string  `json:"name" binding:"required,max=255"`
	Price      float64 `json:"price" binding:"required,min=0"`
	Attributes JSONMap `json:"attributes" binding:"required"`
	ImageURL   *string `json:"image_url" binding:"omitempty,url"`
}

// ProductListRequest 商品列表请求
type ProductListRequest struct {
	CategoryID *string        `form:"category_id" binding:"omitempty,uuid"`
	Status     *ProductStatus `form:"status" binding:"omitempty,oneof=DRAFT ACTIVE INACTIVE OUT_OF_STOCK DISCONTINUED DELETED"`
	Brand      *string        `form:"brand" binding:"omitempty,max=100"`
	MinPrice   *float64       `form:"min_price" binding:"omitempty,min=0"`
	MaxPrice   *float64       `form:"max_price" binding:"omitempty,min=0"`
	Sort       *string        `form:"sort" binding:"omitempty,oneof=created_at updated_at price view_count sale_count"`
	Order      *string        `form:"order" binding:"omitempty,oneof=asc desc"`
	Page       int            `form:"page" binding:"omitempty,min=1"`
	PageSize   int            `form:"page_size" binding:"omitempty,min=1,max=100"`
}

// ProductSearchRequest 商品搜索请求
type ProductSearchRequest struct {
	Query      string   `form:"q" binding:"required,min=1,max=100"`
	CategoryID *string  `form:"category_id" binding:"omitempty,uuid"`
	Brand      *string  `form:"brand" binding:"omitempty,max=100"`
	MinPrice   *float64 `form:"min_price" binding:"omitempty,min=0"`
	MaxPrice   *float64 `form:"max_price" binding:"omitempty,min=0"`
	Sort       *string  `form:"sort" binding:"omitempty,oneof=relevance price view_count sale_count"`
	Order      *string  `form:"order" binding:"omitempty,oneof=asc desc"`
	Page       int      `form:"page" binding:"omitempty,min=1"`
	PageSize   int      `form:"page_size" binding:"omitempty,min=1,max=100"`
}

// ProductResponse 商品响应
type ProductResponse struct {
	*Product
	DiscountPercentage *float64 `json:"discount_percentage,omitempty"`
	IsOnSale           bool     `json:"is_on_sale"`
	VariantCount       int      `json:"variant_count"`
}

// Validate 验证商品数据
func (p *Product) Validate() error {
	if p.Name == "" {
		return fmt.Errorf("product name is required")
	}

	if p.Price < 0 {
		return fmt.Errorf("product price must be non-negative")
	}

	if p.CategoryID == "" {
		return fmt.Errorf("category ID is required")
	}

	return nil
}

// GetDiscountPercentage 计算折扣百分比
func (p *Product) GetDiscountPercentage() *float64 {
	if p.OriginalPrice == nil || *p.OriginalPrice <= p.Price {
		return nil
	}

	discount := ((*p.OriginalPrice - p.Price) / *p.OriginalPrice) * 100
	return &discount
}

// IsOnSale 判断是否在促销
func (p *Product) IsOnSale() bool {
	return p.OriginalPrice != nil && *p.OriginalPrice > p.Price
}

// Value 实现 driver.Valuer 接口 - JSONMap
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口 - JSONMap
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}

	return json.Unmarshal(bytes, j)
}

// Value 实现 driver.Valuer 接口 - JSONArray
func (j JSONArray) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口 - JSONArray
func (j *JSONArray) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONArray", value)
	}

	return json.Unmarshal(bytes, j)
}

// Value 实现 driver.Valuer 接口 - ProductStatus
func (ps ProductStatus) Value() (driver.Value, error) {
	return string(ps), nil
}

// Scan 实现 sql.Scanner 接口 - ProductStatus
func (ps *ProductStatus) Scan(value interface{}) error {
	if value == nil {
		*ps = ProductStatusActive
		return nil
	}

	switch s := value.(type) {
	case string:
		*ps = ProductStatus(s)
	case []byte:
		*ps = ProductStatus(s)
	default:
		return fmt.Errorf("cannot scan %T into ProductStatus", value)
	}

	return nil
}

// ProductCreateRequest 创建商品请求
type ProductCreateRequest struct {
	Name          string         `json:"name" binding:"required,max=255"`
	Description   *string        `json:"description" binding:"omitempty,max=2000"`
	Price         float64        `json:"price" binding:"required,min=0"`
	OriginalPrice *float64       `json:"original_price" binding:"omitempty,min=0"`
	ImageURL      *string        `json:"image_url" binding:"omitempty,url"`
	ImageURLs     *JSONArray     `json:"image_urls" binding:"omitempty"`
	CategoryID    string         `json:"category_id" binding:"required,uuid"`
	Brand         *string        `json:"brand" binding:"omitempty,max=100"`
	SKU           *string        `json:"sku" binding:"omitempty,max=100"`
	Weight        *float64       `json:"weight" binding:"omitempty,min=0"`
	Dimensions    *JSONMap       `json:"dimensions" binding:"omitempty"`
	Tags          *JSONArray     `json:"tags" binding:"omitempty"`
	Attributes    *JSONMap       `json:"attributes" binding:"omitempty"`
	Status        *ProductStatus `json:"status" binding:"omitempty,oneof=DRAFT ACTIVE INACTIVE OUT_OF_STOCK DISCONTINUED DELETED"`
	SortOrder     *int           `json:"sort_order" binding:"omitempty,min=0"`
}

// ProductUpdateRequest 更新商品请求
type ProductUpdateRequest struct {
	Name          *string    `json:"name" binding:"omitempty,max=255"`
	Description   *string    `json:"description" binding:"omitempty,max=2000"`
	Price         *float64   `json:"price" binding:"omitempty,min=0"`
	OriginalPrice *float64   `json:"original_price" binding:"omitempty,min=0"`
	ImageURL      *string    `json:"image_url" binding:"omitempty,url"`
	ImageURLs     *JSONArray `json:"image_urls" binding:"omitempty"`
	CategoryID    *string    `json:"category_id" binding:"omitempty,uuid"`
	Brand         *string    `json:"brand" binding:"omitempty,max=100"`
	SKU           *string    `json:"sku" binding:"omitempty,max=100"`
	Weight        *float64   `json:"weight" binding:"omitempty,min=0"`
	Dimensions    *JSONMap   `json:"dimensions" binding:"omitempty"`
	Tags          *JSONArray `json:"tags" binding:"omitempty"`
	Attributes    *JSONMap   `json:"attributes" binding:"omitempty"`
	SortOrder     *int       `json:"sort_order" binding:"omitempty,min=0"`
}

// ProductListResponse 商品列表响应
type ProductListResponse struct {
	Products []*Product `json:"products"`
	Total    int        `json:"total"`
	Page     int        `json:"page"`
	PageSize int        `json:"page_size"`
}

// ProductSearchResponse 商品搜索响应
type ProductSearchResponse struct {
	Products []*Product `json:"products"`
	Total    int        `json:"total"`
	Page     int        `json:"page"`
	PageSize int        `json:"page_size"`
	Query    string     `json:"query"`
}

// ProductStatusUpdateRequest 商品状态更新请求
type ProductStatusUpdateRequest struct {
	Status ProductStatus `json:"status" binding:"required"`
	Reason string        `json:"reason" binding:"omitempty,max=500"`
}

// Validate 验证创建商品请求
func (req *ProductCreateRequest) Validate() error {
	if req.Name == "" {
		return fmt.Errorf("product name is required")
	}
	if req.Price < 0 {
		return fmt.Errorf("product price must be non-negative")
	}
	if req.CategoryID == "" {
		return fmt.Errorf("category ID is required")
	}
	if req.OriginalPrice != nil && *req.OriginalPrice < req.Price {
		return fmt.Errorf("original price must be greater than or equal to current price")
	}
	return nil
}

// Validate 验证更新商品请求
func (req *ProductUpdateRequest) Validate() error {
	if req.Name != nil && *req.Name == "" {
		return fmt.Errorf("product name cannot be empty")
	}
	if req.Price != nil && *req.Price < 0 {
		return fmt.Errorf("product price must be non-negative")
	}
	if req.OriginalPrice != nil && req.Price != nil && *req.OriginalPrice < *req.Price {
		return fmt.Errorf("original price must be greater than or equal to current price")
	}
	return nil
}

// Validate 验证商品列表请求
func (req *ProductListRequest) Validate() error {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	if req.MinPrice != nil && req.MaxPrice != nil && *req.MinPrice > *req.MaxPrice {
		return fmt.Errorf("min price cannot be greater than max price")
	}
	return nil
}

// Validate 验证商品搜索请求
func (req *ProductSearchRequest) Validate() error {
	if req.Query == "" {
		return fmt.Errorf("search query is required")
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	if req.MinPrice != nil && req.MaxPrice != nil && *req.MinPrice > *req.MaxPrice {
		return fmt.Errorf("min price cannot be greater than max price")
	}
	return nil
}
