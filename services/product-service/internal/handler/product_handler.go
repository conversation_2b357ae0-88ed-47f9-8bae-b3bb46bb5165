package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/service"
)

// ProductHandler 商品处理器
type ProductHandler struct {
	productService service.ProductService
}

// NewProductHandler 创建商品处理器实例
func NewProductHandler(productService service.ProductService) *ProductHandler {
	return &ProductHandler{
		productService: productService,
	}
}

// Create 创建商品
// @Summary 创建商品
// @Description 创建新的商品
// @Tags products
// @Accept json
// @Produce json
// @Param product body model.ProductCreateRequest true "商品信息"
// @Success 201 {object} model.ProductResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products [post]
func (h *ProductHandler) Create(c *gin.Context) {
	var req model.ProductCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>N(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: err.Error(),
		})
		return
	}

	product, err := h.productService.Create(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, model.ProductResponse{
		Product: product,
	})
}

// GetByID 根据ID获取商品
// @Summary 获取商品详情
// @Description 根据ID获取商品详细信息
// @Tags products
// @Produce json
// @Param id path string true "商品ID"
// @Success 200 {object} model.ProductResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/{id} [get]
func (h *ProductHandler) GetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Product ID is required",
		})
		return
	}

	product, err := h.productService.GetByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "PRODUCT_NOT_FOUND",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.ProductResponse{
		Product: product,
	})
}

// GetBySKU 根据SKU获取商品
// @Summary 根据SKU获取商品
// @Description 根据SKU获取商品详细信息
// @Tags products
// @Produce json
// @Param sku path string true "商品SKU"
// @Success 200 {object} model.ProductResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/sku/{sku} [get]
func (h *ProductHandler) GetBySKU(c *gin.Context) {
	sku := c.Param("sku")
	if sku == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_SKU",
			Message: "Product SKU is required",
		})
		return
	}

	product, err := h.productService.GetBySKU(c.Request.Context(), sku)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "PRODUCT_NOT_FOUND",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.ProductResponse{
		Product: product,
	})
}

// List 获取商品列表
// @Summary 获取商品列表
// @Description 分页获取商品列表
// @Tags products
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param category_id query string false "分类ID"
// @Param status query string false "商品状态"
// @Param brand query string false "品牌"
// @Param min_price query number false "最低价格"
// @Param max_price query number false "最高价格"
// @Param sort query string false "排序字段"
// @Param order query string false "排序方向" Enums(asc, desc)
// @Success 200 {object} model.ProductListResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products [get]
func (h *ProductHandler) List(c *gin.Context) {
	req := &model.ProductListRequest{
		Page:     1,
		PageSize: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			req.PageSize = ps
		}
	}

	if categoryID := c.Query("category_id"); categoryID != "" {
		req.CategoryID = &categoryID
	}

	if status := c.Query("status"); status != "" {
		productStatus := model.ProductStatus(status)
		req.Status = &productStatus
	}

	if brand := c.Query("brand"); brand != "" {
		req.Brand = &brand
	}

	if minPrice := c.Query("min_price"); minPrice != "" {
		if mp, err := strconv.ParseFloat(minPrice, 64); err == nil {
			req.MinPrice = &mp
		}
	}

	if maxPrice := c.Query("max_price"); maxPrice != "" {
		if mp, err := strconv.ParseFloat(maxPrice, 64); err == nil {
			req.MaxPrice = &mp
		}
	}

	if sort := c.Query("sort"); sort != "" {
		req.Sort = &sort
	}

	if order := c.Query("order"); order != "" {
		req.Order = &order
	}

	response, err := h.productService.List(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// Search 搜索商品
// @Summary 搜索商品
// @Description 全文搜索商品
// @Tags products
// @Produce json
// @Param q query string true "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param category_id query string false "分类ID"
// @Param brand query string false "品牌"
// @Param min_price query number false "最低价格"
// @Param max_price query number false "最高价格"
// @Param sort query string false "排序字段"
// @Param order query string false "排序方向" Enums(asc, desc)
// @Success 200 {object} model.ProductListResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/search [get]
func (h *ProductHandler) Search(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_QUERY",
			Message: "Search query is required",
		})
		return
	}

	req := &model.ProductSearchRequest{
		Query:    query,
		Page:     1,
		PageSize: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			req.PageSize = ps
		}
	}

	if categoryID := c.Query("category_id"); categoryID != "" {
		req.CategoryID = &categoryID
	}

	if brand := c.Query("brand"); brand != "" {
		req.Brand = &brand
	}

	if minPrice := c.Query("min_price"); minPrice != "" {
		if mp, err := strconv.ParseFloat(minPrice, 64); err == nil {
			req.MinPrice = &mp
		}
	}

	if maxPrice := c.Query("max_price"); maxPrice != "" {
		if mp, err := strconv.ParseFloat(maxPrice, 64); err == nil {
			req.MaxPrice = &mp
		}
	}

	if sort := c.Query("sort"); sort != "" {
		req.Sort = &sort
	}

	if order := c.Query("order"); order != "" {
		req.Order = &order
	}

	response, err := h.productService.Search(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "SEARCH_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// Update 更新商品
// @Summary 更新商品
// @Description 更新商品信息
// @Tags products
// @Accept json
// @Produce json
// @Param id path string true "商品ID"
// @Param product body model.ProductUpdateRequest true "更新信息"
// @Success 200 {object} model.ProductResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/{id} [put]
func (h *ProductHandler) Update(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Product ID is required",
		})
		return
	}

	var req model.ProductUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: err.Error(),
		})
		return
	}

	product, err := h.productService.Update(c.Request.Context(), id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.ProductResponse{
		Product: product,
	})
}

// Delete 删除商品
// @Summary 删除商品
// @Description 删除指定商品
// @Tags products
// @Produce json
// @Param id path string true "商品ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/{id} [delete]
func (h *ProductHandler) Delete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Product ID is required",
		})
		return
	}

	err := h.productService.Delete(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DELETE_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// UpdateStatus 更新商品状态
// @Summary 更新商品状态
// @Description 更新商品的状态
// @Tags products
// @Accept json
// @Produce json
// @Param id path string true "商品ID"
// @Param status body model.ProductStatusUpdateRequest true "状态信息"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/{id}/status [patch]
func (h *ProductHandler) UpdateStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Product ID is required",
		})
		return
	}

	var req model.ProductStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: err.Error(),
		})
		return
	}

	err := h.productService.UpdateStatus(c.Request.Context(), id, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_STATUS_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetPopular 获取热门商品
// @Summary 获取热门商品
// @Description 获取热门商品列表
// @Tags products
// @Produce json
// @Param limit query int false "数量限制" default(10)
// @Success 200 {object} model.ProductListResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/popular [get]
func (h *ProductHandler) GetPopular(c *gin.Context) {
	limit := 10
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	products, err := h.productService.GetPopular(c.Request.Context(), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_POPULAR_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.ProductListResponse{
		Products: products,
		Total:    len(products),
		Page:     1,
		PageSize: len(products),
	})
}

// GetByCategory 根据分类获取商品
// @Summary 根据分类获取商品
// @Description 获取指定分类下的商品列表
// @Tags products
// @Produce json
// @Param category_id path string true "分类ID"
// @Param limit query int false "数量限制" default(20)
// @Success 200 {object} model.ProductListResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/category/{category_id} [get]
func (h *ProductHandler) GetByCategory(c *gin.Context) {
	categoryID := c.Param("category_id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_CATEGORY_ID",
			Message: "Category ID is required",
		})
		return
	}

	limit := 20
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	products, err := h.productService.GetByCategory(c.Request.Context(), categoryID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_BY_CATEGORY_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.ProductListResponse{
		Products: products,
		Total:    len(products),
		Page:     1,
		PageSize: len(products),
	})
}

// IncrementViewCount 增加商品浏览次数
// @Summary 增加商品浏览次数
// @Description 增加指定商品的浏览次数
// @Tags products
// @Produce json
// @Param id path string true "商品ID"
// @Success 200
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /products/{id}/view [post]
func (h *ProductHandler) IncrementViewCount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Product ID is required",
		})
		return
	}

	err := h.productService.IncrementViewCount(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "INCREMENT_VIEW_COUNT_FAILED",
			Message: err.Error(),
		})
		return
	}

	c.Status(http.StatusOK)
}
