package epoll

import (
	"fmt"
	"net"
	"sync"
	"syscall"
	"testing"
	"time"
)

// MyEventHandler 是 EventHandler 接口的一个简单实现，用于测试
type MyEventHandler struct {
	mu              sync.Mutex
	connectCount    int
	readCount       int
	closeCount      int
	errorCount      int
	receivedData    []byte
	onConnectErr    error // 模拟 OnConnect 错误
	onReadErr       error // 模拟 OnRead 错误
	onCloseErr      error // 模拟 OnClose 错误
	lastError       error // 记录最后一次错误
	connectedConns  map[int]*Connection
	closedConns     map[int]*Connection
	readDataMap     map[int][]byte
}

func NewMyEventHandler() *MyEventHandler {
	return &MyEventHandler{
		connectedConns: make(map[int]*Connection),
		closedConns:    make(map[int]*Connection),
		readDataMap:    make(map[int][]byte),
	}
}

func (h *MyEventHandler) OnConnect(conn *Connection) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.connectCount++
	h.connectedConns[conn.fd] = conn
	return h.onConnectErr
}

func (h *MyEventHandler) OnRead(conn *Connection, data []byte) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.readCount++
	h.receivedData = append(h.receivedData, data...)
	h.readDataMap[conn.fd] = append(h.readDataMap[conn.fd], data...)

	// 简单回显数据
	_, err := syscall.Write(conn.fd, data)
	if err != nil {
		return fmt.Errorf("回显数据到文件描述符 %d 错误: %v", conn.fd, err)
	}
	return h.onReadErr
}

func (h *MyEventHandler) OnClose(conn *Connection) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.closeCount++
	h.closedConns[conn.fd] = conn
	delete(h.connectedConns, conn.fd)
	return h.onCloseErr
}

func (h *MyEventHandler) OnError(conn *Connection, err error) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.errorCount++
	h.lastError = err
}

func createClientAndConnect(t *testing.T, port string) net.Conn {
	clientConn, err := net.Dial("tcp", "127.0.0.1"+port)
	if err != nil {
		t.Fatalf("客户端连接服务器失败: %v", err)
	}
	return clientConn
}

// getFreePort 获取一个可用的随机端口
func getFreePort() (string, error) {
	addr, err := net.ResolveTCPAddr("tcp", "localhost:0")
	if err != nil {
		return "", err
	}
	l, err := net.ListenTCP("tcp", addr)
	if err != nil {
		return "", err
	}
	defer l.Close()
	return fmt.Sprintf(":%d", l.Addr().(*net.TCPAddr).Port), nil
}

func TestEpollServer(t *testing.T) {
	PORT, err := getFreePort()
	if err != nil {
		t.Fatalf("获取空闲端口失败: %v", err)
	}

	handler := NewMyEventHandler()
	server, err := NewEpollServer(PORT, handler)
	if err != nil {
		t.Fatalf("创建 Epoll 服务器失败: %v", err)
	}
	defer server.Close() // 确保服务器在测试结束时关闭

	// 在 Goroutine 中启动服务器，以便测试可以继续执行
	go server.Start()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	t.Run("基本连接和数据传输测试", func(t *testing.T) {
		clientConn := createClientAndConnect(t, PORT)
		defer clientConn.Close()

		// 等待 OnConnect 事件被处理
		time.Sleep(100 * time.Millisecond)
		handler.mu.Lock()
		if handler.connectCount != 1 {
			t.Errorf("预期 connectCount 为 1，实际为 %d", handler.connectCount)
		}
		handler.mu.Unlock()

		// 客户端发送数据
		testMessage := "Hello Epoll Server!"
		_, err = clientConn.Write([]byte(testMessage))
		if err != nil {
			t.Fatalf("客户端发送数据失败: %v", err)
		}

		// 等待 OnRead 事件被处理并回显数据
		time.Sleep(100 * time.Millisecond)

		// 客户端读取回显数据
		readBuffer := make([]byte, 1024)
		n, err := clientConn.Read(readBuffer)
		if err != nil {
			t.Fatalf("客户端读取回显数据失败: %v", err)
		}
		receivedEcho := string(readBuffer[:n])

		handler.mu.Lock()
		if handler.readCount != 1 {
			t.Errorf("预期 readCount 为 1，实际为 %d", handler.readCount)
		}
		if string(handler.receivedData) != testMessage {
			t.Errorf("预期接收到的数据为 %q，实际为 %q", testMessage, string(handler.receivedData))
		}
		handler.mu.Unlock()

		if receivedEcho != testMessage {
			t.Errorf("预期回显数据为 %q，实际为 %q", testMessage, receivedEcho)
		}

		// 关闭客户端连接
		clientConn.Close()

		// 等待 OnClose 事件被处理
		time.Sleep(100 * time.Millisecond)
		handler.mu.Lock()
		if handler.closeCount != 1 {
			t.Errorf("预期 closeCount 为 1，实际为 %d", handler.closeCount)
		}
		handler.mu.Unlock()

		// 确保没有错误发生
		handler.mu.Lock()
		if handler.errorCount != 0 {
			t.Errorf("预期 errorCount 为 0，实际为 %d", handler.errorCount)
		}
		handler.mu.Unlock()
	})

	t.Run("OnConnect回调错误测试", func(t *testing.T) {
		port, err := getFreePort()
		if err != nil {
			t.Fatalf("获取空闲端口失败: %v", err)
		}
		handler = NewMyEventHandler()
		handler.onConnectErr = fmt.Errorf("模拟 OnConnect 错误")
		server, err := NewEpollServer(port, handler)
		if err != nil {
			t.Fatalf("创建 Epoll 服务器失败: %v", err)
		}
		defer server.Close()
		go server.Start()
		time.Sleep(100 * time.Millisecond)

		clientConn := createClientAndConnect(t, port)
		defer clientConn.Close()

		time.Sleep(1 * time.Second) // 给予足够时间处理连接和错误
		handler.mu.Lock()
		if handler.connectCount != 1 {
			t.Errorf("预期 connectCount 为 1，实际为 %d", handler.connectCount)
		}
		if handler.errorCount != 1 {
			t.Errorf("预期 errorCount 为 1，实际为 %d", handler.errorCount)
		}
		if handler.lastError == nil || handler.lastError.Error() != "模拟 OnConnect 错误" {
			t.Errorf("预期 lastError 为 '模拟 OnConnect 错误'，实际为 %v", handler.lastError)
		}
		// 验证连接是否被关闭
		if _, ok := handler.connectedConns[getConnectionFD(clientConn)]; ok {
			t.Errorf("预期连接在 OnConnect 错误后被关闭，但仍在 connectedConns 中")
		}
		handler.mu.Unlock()
	})

	t.Run("OnRead回调错误测试", func(t *testing.T) {
		port, err := getFreePort()
		if err != nil {
			t.Fatalf("获取空闲端口失败: %v", err)
		}
		handler = NewMyEventHandler()
		handler.onReadErr = fmt.Errorf("模拟 OnRead 错误")
		server, err := NewEpollServer(port, handler)
		if err != nil {
			t.Fatalf("创建 Epoll 服务器失败: %v", err)
		}
		defer server.Close()
		go server.Start()
		time.Sleep(100 * time.Millisecond)

		clientConn := createClientAndConnect(t, port)
		defer clientConn.Close()
		time.Sleep(100 * time.Millisecond) // 等待连接建立

		testMessage := "Data for OnRead error"
		_, err = clientConn.Write([]byte(testMessage))
		if err != nil {
			t.Fatalf("客户端发送数据失败: %v", err)
		}

		time.Sleep(1 * time.Second) // 给予足够时间处理读事件和错误
		handler.mu.Lock()
		if handler.readCount != 1 {
			t.Errorf("预期 readCount 为 1，实际为 %d", handler.readCount)
		}
		if handler.errorCount != 1 {
			t.Errorf("预期 errorCount 为 1，实际为 %d", handler.errorCount)
		}
		if handler.lastError == nil || handler.lastError.Error() != "模拟 OnRead 错误" {
			t.Errorf("预期 lastError 为 '模拟 OnRead 错误'，实际为 %v", handler.lastError)
		}
		// 验证连接是否被关闭
		if _, ok := handler.connectedConns[getConnectionFD(clientConn)]; ok {
			t.Errorf("预期连接在 OnRead 错误后被关闭，但仍在 connectedConns 中")
		}
		handler.mu.Unlock()
	})

	t.Run("OnClose回调错误测试", func(t *testing.T) {
		port, err := getFreePort()
		if err != nil {
			t.Fatalf("获取空闲端口失败: %v", err)
		}
		handler = NewMyEventHandler()
		handler.onCloseErr = fmt.Errorf("模拟 OnClose 错误")
		server, err := NewEpollServer(port, handler)
		if err != nil {
			t.Fatalf("创建 Epoll 服务器失败: %v", err)
		}
		defer server.Close()
		go server.Start()
		time.Sleep(100 * time.Millisecond)

		clientConn := createClientAndConnect(t, port)
		time.Sleep(100 * time.Millisecond) // 等待连接建立

		clientConn.Close() // 客户端主动关闭连接

		time.Sleep(200 * time.Millisecond) // 给予足够时间处理关闭事件和错误
		handler.mu.Lock()
		if handler.closeCount != 1 {
			t.Errorf("预期 closeCount 为 1，实际为 %d", handler.closeCount)
		}
		if handler.errorCount != 0 { // OnClose 错误不会通过 OnError 报告，因为连接已经关闭
			t.Errorf("预期 errorCount 为 0，实际为 %d", handler.errorCount)
		}
		handler.mu.Unlock()
	})

	t.Run("多客户端并发连接测试", func(t *testing.T) {
		port, err := getFreePort()
		if err != nil {
			t.Fatalf("获取空闲端口失败: %v", err)
		}
		handler = NewMyEventHandler()
		server, err := NewEpollServer(port, handler)
		if err != nil {
			t.Fatalf("创建 Epoll 服务器失败: %v", err)
		}
		defer server.Close()
		go server.Start()
		time.Sleep(100 * time.Millisecond)

		numClients := 10
		var clientWg sync.WaitGroup
		messages := make(chan string, numClients)

		for i := 0; i < numClients; i++ {
			clientWg.Add(1)
			go func(clientID int) {
				defer clientWg.Done()
				clientConn := createClientAndConnect(t, port)
				defer clientConn.Close()

				msg := fmt.Sprintf("Client %d: Hello from concurrent test!", clientID)
				_, err := clientConn.Write([]byte(msg))
				if err != nil {
					t.Errorf("客户端 %d 发送数据失败: %v", clientID, err)
					return
				}

				readBuffer := make([]byte, 1024)
				n, err := clientConn.Read(readBuffer)
				if err != nil {
					t.Errorf("客户端 %d 读取回显数据失败: %v", clientID, err)
					return
				}
				receivedEcho := string(readBuffer[:n])
				messages <- receivedEcho
			}(i)
		}

		clientWg.Wait()
		close(messages)

		time.Sleep(200 * time.Millisecond) // 给予足够时间处理所有事件

		handler.mu.Lock()
		if handler.connectCount != numClients {
			t.Errorf("预期 connectCount 为 %d，实际为 %d", numClients, handler.connectCount)
		}
		if handler.readCount != numClients {
			t.Errorf("预期 readCount 为 %d，实际为 %d", numClients, handler.readCount)
		}
		if handler.closeCount != numClients {
			t.Errorf("预期 closeCount 为 %d，实际为 %d", numClients, handler.closeCount)
		}
		if handler.errorCount != 0 {
			t.Errorf("预期 errorCount 为 0，实际为 %d", handler.errorCount)
		}
		handler.mu.Unlock()

		// 验证所有回显消息
		receivedEchoes := make(map[string]struct{})
		for msg := range messages {
			receivedEchoes[msg] = struct{}{}
		}
		if len(receivedEchoes) != numClients {
			t.Errorf("预期收到 %d 条唯一回显消息，实际为 %d", numClients, len(receivedEchoes))
		}
	})

	t.Run("大数据传输测试", func(t *testing.T) {
		port, err := getFreePort()
		if err != nil {
			t.Fatalf("获取空闲端口失败: %v", err)
		}
		handler = NewMyEventHandler()
		server, err := NewEpollServer(port, handler)
		if err != nil {
			t.Fatalf("创建 Epoll 服务器失败: %v", err)
		}
		defer server.Close()
		go server.Start()
		time.Sleep(100 * time.Millisecond)

		clientConn := createClientAndConnect(t, port)
		defer clientConn.Close()
		time.Sleep(100 * time.Millisecond)

		largeMessage := make([]byte, 4096) // 4KB 数据
		for i := 0; i < len(largeMessage); i++ {
			largeMessage[i] = byte(i % 256)
		}

		_, err = clientConn.Write(largeMessage)
		if err != nil {
			t.Fatalf("客户端发送大数据失败: %v", err)
		}

		time.Sleep(200 * time.Millisecond) // 给予足够时间处理大数据

		readBuffer := make([]byte, 8192) // 确保缓冲区足够大
		n, err := clientConn.Read(readBuffer)
		if err != nil {
			t.Fatalf("客户端读取回显大数据失败: %v", err)
		}
		receivedEcho := readBuffer[:n]

		handler.mu.Lock()
		if handler.readCount != 1 {
			t.Errorf("预期 readCount 为 1，实际为 %d", handler.readCount)
		}
		if len(handler.receivedData) != len(largeMessage) {
			t.Errorf("预期接收到的数据长度为 %d，实际为 %d", len(largeMessage), len(handler.receivedData))
		}
		for i := 0; i < len(largeMessage); i++ {
			if handler.receivedData[i] != largeMessage[i] {
				t.Errorf("接收到的数据在索引 %d 处不匹配", i)
				break
			}
		}
		handler.mu.Unlock()

		if len(receivedEcho) != len(largeMessage) {
			t.Errorf("预期回显数据长度为 %d，实际为 %d", len(largeMessage), len(receivedEcho))
		}
		for i := 0; i < len(largeMessage); i++ {
			if receivedEcho[i] != largeMessage[i] {
				t.Errorf("回显数据在索引 %d 处不匹配", i)
				break
			}
		}
	})

	t.Run("服务器优雅关闭测试", func(t *testing.T) {
		port, err := getFreePort()
		if err != nil {
			t.Fatalf("获取空闲端口失败: %v", err)
		}
		handler = NewMyEventHandler()
		server, err := NewEpollServer(port, handler)
		if err != nil {
			t.Fatalf("创建 Epoll 服务器失败: %v", err)
		}
		go server.Start()
		time.Sleep(100 * time.Millisecond)

		clientConn := createClientAndConnect(t, port)
		// defer clientConn.Close() // 延迟关闭可能导致测试时序问题，在此处显式关闭
		time.Sleep(100 * time.Millisecond)

		// 关闭服务器
		server.Close()
		time.Sleep(1 * time.Second) // 增加等待时间，确保关闭操作完成

		// 显式关闭客户端连接，确保其感知到服务器关闭
		clientConn.Close()

		// 尝试向已关闭的服务器发送数据，应该会失败
		_, err = clientConn.Write([]byte("Should fail"))
		if err == nil {
			t.Error("预期向已关闭的服务器发送数据会失败，但成功了")
		}

		// 验证连接是否被清理
		handler.mu.Lock()
		if len(server.connections) != 0 {
			t.Errorf("预期所有连接在服务器关闭后被清理，实际有 %d 个连接", len(server.connections))
		}
		handler.mu.Unlock()

		// 再次尝试连接，应该失败
		_, err = net.Dial("tcp", "127.0.0.1"+port)
		if err == nil {
			t.Error("预期无法连接到已关闭的服务器，但成功了")
		}
	})

	t.Run("客户端主动关闭连接测试", func(t *testing.T) {
		port, err := getFreePort()
		if err != nil {
			t.Fatalf("获取空闲端口失败: %v", err)
		}
		handler = NewMyEventHandler()
		server, err := NewEpollServer(port, handler)
		if err != nil {
			t.Fatalf("创建 Epoll 服务器失败: %v", err)
		}
		defer server.Close()
		go server.Start()
		time.Sleep(100 * time.Millisecond)

		clientConn := createClientAndConnect(t, port)
		time.Sleep(100 * time.Millisecond) // 等待连接建立

		clientConn.Close() // 客户端主动关闭

		time.Sleep(200 * time.Millisecond) // 给予足够时间处理关闭事件

		handler.mu.Lock()
		if handler.closeCount != 1 {
			t.Errorf("预期 closeCount 为 1，实际为 %d", handler.closeCount)
		}
		if handler.errorCount != 0 {
			t.Errorf("预期 errorCount 为 0，实际为 %d", handler.errorCount)
		}
		// 验证服务器端连接是否被清理
		if len(server.connections) != 0 {
			t.Errorf("预期服务器端连接在客户端关闭后被清理，实际有 %d 个连接", len(server.connections))
		}
		handler.mu.Unlock()
	})
}

// getConnectionFD 从 net.Conn 获取文件描述符 (仅限 TCPConn)
func getConnectionFD(conn net.Conn) int {
	tcpConn, ok := conn.(*net.TCPConn)
	if !ok {
		fmt.Printf("getConnectionFD: 连接不是 TCPConn 类型\n")
		return -1
	}
	file, err := tcpConn.File()
	if err != nil {
		fmt.Printf("getConnectionFD: 获取文件失败: %v\n", err)
		return -1
	}
	// 注意：这里不再 defer file.Close()，因为这可能导致文件描述符过早关闭
	fd := int(file.Fd())
	fmt.Printf("getConnectionFD: 获取到文件描述符: %d\n", fd)
	return fd
}
