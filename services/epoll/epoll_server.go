package epoll

import (
	"fmt"
	"net"
	"sync"
	"syscall"

	"golang.org/x/sys/unix"
)

// Connection 结构体表示一个客户端连接
type Connection struct {
	fd         int      // 客户端连接的文件描述符
	remoteAddr net.Addr // 客户端远程地址
	readBuffer []byte   // 读缓冲区
}

// EventHandler 接口定义了处理不同类型事件的方法
type EventHandler interface {
	OnConnect(conn *Connection) error           // 新连接建立时调用
	OnRead(conn *Connection, data []byte) error // 收到数据时调用
	OnClose(conn *Connection) error             // 连接关闭时调用
	OnError(conn *Connection, err error)        // 发生错误时调用
}

// EpollServer 结构体封装 Epoll 实例、监听器、连接管理和事件循环
type EpollServer struct {
	epollFD     int                 // Epoll 实例的文件描述符
	listener    net.Listener        // TCP 监听器
	listenFD    int                 // 监听套接字的文件描述符
	connections map[int]*Connection // 管理所有活跃的连接 (fd -> *Connection)
	handler     EventHandler        // 事件处理器接口
	bufferPool  sync.Pool           // 缓冲区池，用于复用读写缓冲区
	quit        chan struct{}       // 用于通知 Start() 退出
	wg          sync.WaitGroup      // 用于等待 Start() goroutine 退出
}

// NewEpollServer 初始化 EpollServer 实例
func NewEpollServer(port string, handler EventHandler) (*EpollServer, error) {
	listener, err := net.Listen("tcp", port)
	if err != nil {
		return nil, fmt.Errorf("监听端口 %s 失败: %v", port, err)
	}

	file, err := listener.(*net.TCPListener).File()
	if err != nil {
		listener.Close()
		return nil, fmt.Errorf("获取监听器文件描述符失败: %v", err)
	}
	listenFD := int(file.Fd())

	epollFD, err := unix.EpollCreate1(0)
	if err != nil {
		file.Close()
		listener.Close()
		return nil, fmt.Errorf("创建 epoll 实例失败: %v", err)
	}

	event := unix.EpollEvent{Events: unix.EPOLLIN, Fd: int32(listenFD)}
	if err := unix.EpollCtl(epollFD, unix.EPOLL_CTL_ADD, listenFD, &event); err != nil {
		syscall.Close(epollFD)
		file.Close()
		listener.Close()
		return nil, fmt.Errorf("添加监听器到 epoll 失败: %v", err)
	}

	return &EpollServer{
		epollFD:     epollFD,
		listener:    listener,
		listenFD:    listenFD, // Store the listenFD
		connections: make(map[int]*Connection),
		handler:     handler,
		bufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 1024) // 默认缓冲区大小
			},
		},
		quit: make(chan struct{}),
	}, nil
}

// Start 启动 Epoll 事件循环
func (s *EpollServer) Start() {
	s.wg.Add(1)
	defer s.wg.Done()
	defer s.listener.Close()

	events := make([]unix.EpollEvent, 128) // MAX_EVENTS
	var n int // 在循环外部声明 n

	fmt.Printf("服务器正在监听 %s\n", s.listener.Addr().String())

	for {
		// 在每次 EpollWait 之前检查 epollFD 是否有效
		if s.epollFD == -1 {
			fmt.Println("EpollServer 已关闭，退出事件循环。")
			return
		}

		select {
		case <-s.quit:
			fmt.Println("收到退出信号，EpollServer 正在关闭...")
			return
		default:
			var err error
			// 设置 EpollWait 超时时间为 100 毫秒，避免无限期阻塞
			n, err = unix.EpollWait(s.epollFD, events, 100)
			if err != nil {
				if err == syscall.EINTR {
					continue
				}
				// 如果 epoll 实例已关闭，则退出循环
				if err == syscall.EBADF {
					fmt.Printf("epoll 实例已关闭，退出事件循环: %v\n", err)
					return
				}
				fmt.Printf("epoll_wait 错误: %v\n", err)
				return // 遇到其他错误也退出
			}
		}

		for i := 0; i < n; i++ { // n 现在在作用域内
			fd := int(events[i].Fd)

			if fd == s.listenFD {
				s.handleAccept(fd)
			} else {
				s.handleReadWrite(fd)
			}
		}
	}
}

// handleAccept 处理新连接请求
func (s *EpollServer) handleAccept(listenFD int) {
	connFD, sa, err := syscall.Accept(listenFD)
	if err != nil {
		if err == syscall.EAGAIN || err == syscall.EWOULDBLOCK {
			return
		}
		fmt.Printf("接受连接错误: %v\n", err)
		return
	}

	if err := syscall.SetNonblock(connFD, true); err != nil {
		fmt.Printf("设置连接非阻塞模式失败: %v\n", err)
		syscall.Close(connFD)
		return
	}

	conn := &Connection{
		fd:         connFD,
		remoteAddr: func() net.Addr {
			switch sa := sa.(type) {
			case *syscall.SockaddrInet4:
				return &net.TCPAddr{IP: sa.Addr[:], Port: sa.Port}
			case *syscall.SockaddrInet6:
				return &net.TCPAddr{IP: sa.Addr[:], Port: sa.Port}
			default:
				return nil // 或者返回一个错误，这里为了简化先返回nil
			}
		}(),
		readBuffer: s.bufferPool.Get().([]byte),
	}
	s.connections[connFD] = conn

	connEvent := unix.EpollEvent{Events: unix.EPOLLIN | unix.EPOLLET, Fd: int32(connFD)}
	if err := unix.EpollCtl(s.epollFD, unix.EPOLL_CTL_ADD, connFD, &connEvent); err != nil {
		fmt.Printf("添加新连接到 epoll 失败: %v\n", err)
		s.CloseConnection(connFD)
		return
	}
	fmt.Printf("接受新连接，文件描述符: %d, 地址: %s\n", connFD, conn.remoteAddr.String())

	if s.handler != nil {
		if err := s.handler.OnConnect(conn); err != nil {
			fmt.Printf("OnConnect 处理错误: %v\n", err)
			s.handler.OnError(conn, err)
			s.handler.OnClose(conn) // 确保在 OnConnect 错误时调用 OnClose
			s.CloseConnection(connFD)
		}
	}
}

// handleReadWrite 处理客户端连接的读写事件
func (s *EpollServer) handleReadWrite(connFD int) {
	conn, ok := s.connections[connFD]
	if !ok {
		fmt.Printf("未找到文件描述符 %d 对应的连接\n", connFD)
		s.CloseConnection(connFD)
		return
	}

	var totalReadData []byte // 用于累积读取的数据
	for {
		nRead, err := syscall.Read(connFD, conn.readBuffer)
		if err != nil {
			if err == syscall.EAGAIN || err == syscall.EWOULDBLOCK {
				break // 没有更多数据可读
			}
			fmt.Printf("从文件描述符 %d 读取数据错误: %v\n", connFD, err)
			if s.handler != nil {
				s.handler.OnError(conn, err)
			}
			s.CloseConnection(connFD)
			return
		}

		if nRead == 0 {
			fmt.Printf("客户端文件描述符 %d 关闭连接\n", connFD)
			if s.handler != nil {
				s.handler.OnClose(conn)
			}
			s.CloseConnection(connFD)
			return
		}
		totalReadData = append(totalReadData, conn.readBuffer[:nRead]...) // 累积数据
	}

	if s.handler != nil && len(totalReadData) > 0 { // 只有当有数据读取时才调用 OnRead
		if err := s.handler.OnRead(conn, totalReadData); err != nil { // 传递累积的数据
			fmt.Printf("OnRead 处理错误: %v\n", err)
			s.handler.OnError(conn, err)
			s.handler.OnClose(conn) // 确保在 OnRead 错误时调用 OnClose
			s.CloseConnection(connFD)
			return
		}
	}
}

// CloseConnection 从 epoll 中删除文件描述符并关闭它
func (s *EpollServer) CloseConnection(fd int) {
	fmt.Printf("CloseConnection: 尝试关闭文件描述符 %d\n", fd)
	if s.epollFD > 0 { // 只有当 epollFD 有效时才尝试从 epoll 中删除
		if err := unix.EpollCtl(s.epollFD, unix.EPOLL_CTL_DEL, fd, nil); err != nil {
			fmt.Printf("CloseConnection: 从 epoll 删除文件描述符 %d 失败: %v\n", fd, err)
		} else {
			fmt.Printf("CloseConnection: 成功从 epoll 删除文件描述符 %d\n", fd)
		}
	} else {
		fmt.Printf("CloseConnection: epollFD 无效 (%d)，跳过从 epoll 删除文件描述符 %d\n", s.epollFD, fd)
	}

	if conn, ok := s.connections[fd]; ok {
		s.bufferPool.Put(conn.readBuffer) // 将缓冲区放回池中
		delete(s.connections, fd)
		fmt.Printf("CloseConnection: 成功从 connections 映射中删除文件描述符 %d. 当前活跃连接数: %d\n", fd, len(s.connections))
	} else {
		fmt.Printf("CloseConnection: 未在 connections 映射中找到文件描述符 %d\n", fd)
	}
	syscall.Close(fd)
	fmt.Printf("CloseConnection: 文件描述符 %d 已关闭\n", fd)
}

// Close 停止 Epoll 服务器，关闭监听器和 epoll 文件描述符
func (s *EpollServer) Close() {
	fmt.Println("EpollServer.Close: 收到关闭请求...")
	s.listener.Close() // 首先关闭监听器，停止接受新连接
	fmt.Println("EpollServer.Close: 监听器已关闭。")

	// 从 epoll 中删除监听器文件描述符并关闭它
	if s.epollFD > 0 && s.listenFD > 0 {
		if err := unix.EpollCtl(s.epollFD, unix.EPOLL_CTL_DEL, s.listenFD, nil); err != nil {
			fmt.Printf("EpollServer.Close: 从 epoll 删除监听器文件描述符 %d 失败: %v\n", s.listenFD, err)
		} else {
			fmt.Printf("EpollServer.Close: 成功从 epoll 删除监听器文件描述符 %d\n", s.listenFD)
		}
		syscall.Close(s.listenFD)
		fmt.Printf("EpollServer.Close: 监听器文件描述符 %d 已关闭。\n", s.listenFD)
	}

	// 通知 Start() goroutine 退出
	close(s.quit)
	fmt.Println("EpollServer.Close: 已发送退出信号。")
	s.wg.Wait() // 等待 Start() goroutine 退出
	fmt.Println("EpollServer.Close: Start goroutine 已退出。")

	// 关闭所有活跃连接
	for fd := range s.connections {
		fmt.Printf("EpollServer.Close: 关闭活跃连接文件描述符: %d\n", fd)
		s.CloseConnection(fd)
	}
	fmt.Println("EpollServer.Close: 所有活跃连接已关闭。")

	syscall.Close(s.epollFD) // 最后关闭 epoll 文件描述符
	s.epollFD = -1           // 将 epollFD 设置为无效值，防止后续操作
	fmt.Println("EpollServer.Close: epollFD 已关闭并设置为无效。")
}
