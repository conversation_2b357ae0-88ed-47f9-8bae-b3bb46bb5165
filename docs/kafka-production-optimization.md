# Kafka 生产环境性能优化指南

## 📊 当前测试环境 vs 生产环境性能对比

| 环境类型 | 当前耗时 | 优化后预期耗时 | 性能提升 |
|---------|---------|---------------|---------|
| 开发测试 | 9秒 | 2-3秒 | 70% |
| 生产环境 | - | 50-200ms | 99% |

## 🚀 生产环境优化策略

### 1. 基础设施优化

#### 1.1 Kafka 集群配置
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  kafka-1:
    image: apache/kafka:latest
    environment:
      # 性能优化配置
      KAFKA_NUM_NETWORK_THREADS: 8
      KAFKA_NUM_IO_THREADS: 16
      KAFKA_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_SOCKET_RECEIVE_BUFFER_BYTES: 102400
      KAFKA_SOCKET_REQUEST_MAX_BYTES: 104857600
      # 日志配置
      KAFKA_LOG_FLUSH_INTERVAL_MESSAGES: 10000
      KAFKA_LOG_FLUSH_INTERVAL_MS: 1000
      # 副本配置
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_IN_SYNC_REPLICAS: 2
    volumes:
      - kafka-data-1:/var/lib/kafka/data
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
```

**预期性能**: 连接建立 < 100ms

#### 1.2 网络优化
```bash
# 生产环境网络配置
# 1. 使用专用网络
# 2. 启用 TCP 优化
# 3. 配置适当的缓冲区大小
```

**预期性能**: 网络延迟 < 1ms (同机房)

### 2. Producer 优化

#### 2.1 批处理配置
```go
// pkg/kafka/producer.go - 生产环境配置
func NewProducerOptimized(config ProducerConfig) *Producer {
    writer := &kafka.Writer{
        Addr:         kafka.TCP(config.Brokers...),
        Topic:        config.Topic,
        Balancer:     &kafka.LeastBytes{}, // 负载均衡
        // 性能优化配置
        BatchSize:    16384,    // 16KB 批处理
        BatchTimeout: 10 * time.Millisecond, // 10ms 批处理超时
        RequiredAcks: kafka.RequireOne,      // 只需要一个副本确认
        Async:        true,     // 异步发送
        // 压缩配置
        Compression: kafka.Snappy, // 使用 Snappy 压缩
        // 连接池
        MaxAttempts: 3,
        WriteTimeout: 1 * time.Second,
    }
    return &Producer{writer: writer}
}
```

**预期性能**: 单条消息发送 < 5ms

#### 2.2 连接池优化
```go
// pkg/kafka/connection_pool.go
type KafkaConnectionPool struct {
    producers map[string]*kafka.Writer
    consumers map[string]*kafka.Reader
    mutex     sync.RWMutex
}

func (p *KafkaConnectionPool) GetProducer(topic string) *kafka.Writer {
    p.mutex.RLock()
    if producer, exists := p.producers[topic]; exists {
        p.mutex.RUnlock()
        return producer
    }
    p.mutex.RUnlock()
    
    // 创建新的 producer 并缓存
    p.mutex.Lock()
    defer p.mutex.Unlock()
    
    producer := &kafka.Writer{
        Addr:         kafka.TCP("kafka:9092"),
        Topic:        topic,
        BatchSize:    16384,
        BatchTimeout: 10 * time.Millisecond,
    }
    p.producers[topic] = producer
    return producer
}
```

**预期性能**: 连接复用，延迟 < 1ms

### 3. Consumer 优化

#### 3.1 高性能消费配置
```go
// pkg/kafka/consumer.go - 生产环境配置
func NewConsumerOptimized(config ConsumerConfig) *Consumer {
    reader := kafka.NewReader(kafka.ReaderConfig{
        Brokers:     config.Brokers,
        Topic:       config.Topic,
        GroupID:     config.GroupID,
        // 性能优化配置
        MinBytes:    1,      // 立即读取
        MaxBytes:    10e6,   // 10MB 最大批次
        MaxWait:     100 * time.Millisecond, // 最大等待时间
        // 预取配置
        ReadBatchTimeout: 100 * time.Millisecond,
        // 分区配置
        Partition: 0, // 指定分区以减少协调开销
        // 偏移量管理
        CommitInterval: 1 * time.Second,
    })
    
    return &Consumer{reader: reader}
}
```

**预期性能**: 消息消费 < 10ms

#### 3.2 并发消费
```go
// services/user-service/internal/event/consumer.go
type ParallelConsumer struct {
    consumers []*kafka.Reader
    workers   int
}

func (pc *ParallelConsumer) StartConsuming(ctx context.Context) {
    for i := 0; i < pc.workers; i++ {
        go func(workerID int) {
            consumer := pc.consumers[workerID]
            for {
                select {
                case <-ctx.Done():
                    return
                default:
                    msg, err := consumer.ReadMessage(ctx)
                    if err != nil {
                        continue
                    }
                    // 处理消息
                    pc.processMessage(msg)
                }
            }
        }(i)
    }
}
```

**预期性能**: 并发处理，吞吐量提升 5-10x

### 4. 应用层优化

#### 4.1 事件发布优化
```go
// services/user-service/internal/service/user_service.go
type UserService struct {
    eventPublisher EventPublisher
    eventBuffer    chan *event.Event // 事件缓冲区
}

func (s *UserService) publishEventAsync(evt *event.Event) {
    select {
    case s.eventBuffer <- evt:
        // 成功加入缓冲区
    default:
        // 缓冲区满，记录警告但不阻塞主流程
        log.Warn("Event buffer full, dropping event")
    }
}

// 后台批量发送
func (s *UserService) startEventBatchSender() {
    ticker := time.NewTicker(50 * time.Millisecond)
    events := make([]*event.Event, 0, 100)
    
    for {
        select {
        case evt := <-s.eventBuffer:
            events = append(events, evt)
            if len(events) >= 100 { // 批量发送
                s.sendEventBatch(events)
                events = events[:0]
            }
        case <-ticker.C:
            if len(events) > 0 { // 定时发送
                s.sendEventBatch(events)
                events = events[:0]
            }
        }
    }
}
```

**预期性能**: 事件发布不阻塞主流程，< 1ms

#### 4.2 序列化优化
```go
// pkg/kafka/serialization.go
import (
    "github.com/vmihailenco/msgpack/v5" // 比 JSON 快 2-3x
    // 或者使用 protobuf 更快
)

func SerializeEvent(event *EventMessage) ([]byte, error) {
    return msgpack.Marshal(event)
}

func DeserializeEvent(data []byte) (*EventMessage, error) {
    var event EventMessage
    err := msgpack.Unmarshal(data, &event)
    return &event, err
}
```

**预期性能**: 序列化性能提升 2-3x

### 5. 监控和可观测性

#### 5.1 性能指标监控
```go
// pkg/kafka/metrics.go
type KafkaMetrics struct {
    PublishLatency   prometheus.Histogram
    ConsumeLatency   prometheus.Histogram
    MessageThroughput prometheus.Counter
    ErrorRate        prometheus.Counter
}

func (m *KafkaMetrics) RecordPublishLatency(duration time.Duration) {
    m.PublishLatency.Observe(duration.Seconds())
}
```

#### 5.2 健康检查
```go
// pkg/kafka/health.go
func (k *KafkaEventPublisher) HealthCheck() error {
    ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
    defer cancel()
    
    // 发送心跳消息
    return k.producer.WriteMessages(ctx, kafka.Message{
        Key:   []byte("health-check"),
        Value: []byte("ping"),
    })
}
```

## 📈 生产环境性能预期

### 延迟指标
| 操作 | P50 | P95 | P99 |
|------|-----|-----|-----|
| 事件发布 | 2ms | 10ms | 50ms |
| 事件消费 | 5ms | 20ms | 100ms |
| 端到端延迟 | 10ms | 50ms | 200ms |

### 吞吐量指标
| 指标 | 单实例 | 集群 (3节点) |
|------|--------|-------------|
| 消息/秒 | 10,000 | 100,000 |
| MB/秒 | 10 MB | 100 MB |
| 并发连接 | 1,000 | 10,000 |

### 可用性指标
- **可用性**: 99.9% (每月停机 < 45分钟)
- **数据持久性**: 99.999% (3副本 + 2最小同步副本)
- **故障恢复时间**: < 30秒

## 🔧 部署建议

### 1. 硬件配置
```yaml
# 生产环境推荐配置
Kafka Broker:
  CPU: 8 cores
  Memory: 16GB
  Storage: SSD 500GB (每个分区)
  Network: 10Gbps

Application Server:
  CPU: 4 cores  
  Memory: 8GB
  Network: 1Gbps
```

### 2. 容器编排
```yaml
# kubernetes/kafka-cluster.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: kafka
        image: apache/kafka:latest
        resources:
          requests:
            memory: "8Gi"
            cpu: "2"
          limits:
            memory: "16Gi"
            cpu: "4"
        env:
        - name: KAFKA_HEAP_OPTS
          value: "-Xmx8G -Xms8G"
```

### 3. 自动扩缩容
```yaml
# kubernetes/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Pods
    pods:
      metric:
        name: kafka_consumer_lag
      target:
        type: AverageValue
        averageValue: "100"
```

## 🎯 总结

通过以上优化措施，生产环境的Kafka性能可以达到：

- **事件发布延迟**: 2-10ms (vs 当前 9秒)
- **事件消费延迟**: 5-20ms  
- **端到端延迟**: 10-50ms
- **吞吐量**: 10,000+ 消息/秒
- **可用性**: 99.9%

这将为支付商城微服务提供高性能、高可靠的事件驱动架构基础。
