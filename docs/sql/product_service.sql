-- 商品服务数据库设计
-- Product Service Database Schema

-- 创建数据库
CREATE DATABASE IF NOT EXISTS product_service CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE product_service;

-- 商品分类表
CREATE TABLE `categories` (
    `id` VARCHAR(36) PRIMARY KEY NOT NULL COMMENT '分类唯一ID，UUID',
    `name` VARCHAR(100) UNIQUE NOT NULL COMMENT '分类名称',
    `description` TEXT COMMENT '分类描述',
    `parent_id` VARCHAR(36) COMMENT '父分类ID，支持多级分类',
    `level` INT NOT NULL DEFAULT 1 COMMENT '分类层级',
    `path` VARCHAR(500) COMMENT '分类路径，如 "/electronics/phones"',
    `icon_url` VARCHAR(255) COMMENT '分类图标URL',
    `sort_order` INT DEFAULT 0 COMMENT '排序权重',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '分类状态 (ACTIVE, INACTIVE)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_level` (`level`),
    INDEX `idx_status` (`status`),
    INDEX `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 商品表
CREATE TABLE `products` (
    `id` VARCHAR(36) PRIMARY KEY NOT NULL COMMENT '商品唯一ID，UUID',
    `name` VARCHAR(255) NOT NULL COMMENT '商品名称',
    `description` TEXT COMMENT '商品描述',
    `price` DECIMAL(10, 2) NOT NULL COMMENT '商品价格',
    `original_price` DECIMAL(10, 2) COMMENT '商品原价（用于显示折扣）',
    `image_url` VARCHAR(255) COMMENT '商品主图片URL',
    `image_urls` JSON COMMENT '商品多图片URL数组',
    `category_id` VARCHAR(36) NOT NULL COMMENT '商品分类ID',
    `brand` VARCHAR(100) COMMENT '商品品牌',
    `sku` VARCHAR(100) UNIQUE COMMENT '商品SKU编码',
    `weight` DECIMAL(8, 3) COMMENT '商品重量（千克）',
    `dimensions` JSON COMMENT '商品尺寸信息 (长x宽x高)',
    `tags` JSON COMMENT '商品标签数组',
    `attributes` JSON COMMENT '商品属性（颜色、尺码等）',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '商品状态 (DRAFT, ACTIVE, INACTIVE, OUT_OF_STOCK, DISCONTINUED, DELETED)',
    `sort_order` INT DEFAULT 0 COMMENT '排序权重',
    `view_count` INT DEFAULT 0 COMMENT '浏览次数',
    `sale_count` INT DEFAULT 0 COMMENT '销售数量',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE RESTRICT,
    INDEX `idx_category_id` (`category_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_brand` (`brand`),
    INDEX `idx_price` (`price`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_view_count` (`view_count`),
    INDEX `idx_sale_count` (`sale_count`),
    FULLTEXT INDEX `idx_name_description` (`name`, `description`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 商品变体表（支持多规格商品）
CREATE TABLE `product_variants` (
    `id` VARCHAR(36) PRIMARY KEY NOT NULL COMMENT '变体唯一ID，UUID',
    `product_id` VARCHAR(36) NOT NULL COMMENT '商品ID',
    `sku` VARCHAR(100) UNIQUE NOT NULL COMMENT '变体SKU编码',
    `name` VARCHAR(255) NOT NULL COMMENT '变体名称，如 "红色-L码"',
    `price` DECIMAL(10, 2) NOT NULL COMMENT '变体价格',
    `attributes` JSON NOT NULL COMMENT '变体属性 {"color": "red", "size": "L"}',
    `image_url` VARCHAR(255) COMMENT '变体专属图片URL',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '变体状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE,
    INDEX `idx_product_id` (`product_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品变体表';

-- 插入示例数据

-- 插入分类数据
INSERT INTO `categories` (`id`, `name`, `description`, `parent_id`, `level`, `path`, `sort_order`) VALUES
('cat-electronics', '电子产品', '各类电子产品', NULL, 1, '/electronics', 1),
('cat-phones', '智能手机', '各品牌智能手机', 'cat-electronics', 2, '/electronics/phones', 1),
('cat-laptops', '笔记本电脑', '各品牌笔记本电脑', 'cat-electronics', 2, '/electronics/laptops', 2),
('cat-clothing', '服装', '各类服装', NULL, 1, '/clothing', 2),
('cat-mens-clothing', '男装', '男士服装', 'cat-clothing', 2, '/clothing/mens', 1),
('cat-womens-clothing', '女装', '女士服装', 'cat-clothing', 2, '/clothing/womens', 2);

-- 插入商品数据
INSERT INTO `products` (`id`, `name`, `description`, `price`, `original_price`, `category_id`, `brand`, `sku`, `weight`, `tags`, `attributes`) VALUES
('prod-iphone15pro', 'iPhone 15 Pro', '最新款iPhone 15 Pro，搭载A17 Pro芯片', 7999.00, 8999.00, 'cat-phones', 'Apple', 'IPHONE15PRO', 0.187,
 JSON_ARRAY('smartphone', '5G', 'premium'),
 JSON_OBJECT('colors', JSON_ARRAY('Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'), 'storage', JSON_ARRAY('128GB', '256GB', '512GB', '1TB'))),

('prod-macbookpro', 'MacBook Pro 14"', '14英寸MacBook Pro，M3芯片', 14999.00, 16999.00, 'cat-laptops', 'Apple', 'MACBOOKPRO14', 1.55,
 JSON_ARRAY('laptop', 'professional', 'M3'),
 JSON_OBJECT('colors', JSON_ARRAY('Space Gray', 'Silver'), 'memory', JSON_ARRAY('8GB', '16GB', '32GB'), 'storage', JSON_ARRAY('512GB', '1TB', '2TB'))),

('prod-tshirt-basic', '基础款T恤', '100%纯棉基础款T恤，舒适透气', 99.00, 129.00, 'cat-mens-clothing', 'BasicWear', 'TSHIRT-BASIC', 0.2,
 JSON_ARRAY('cotton', 'casual', 'basic'),
 JSON_OBJECT('colors', JSON_ARRAY('White', 'Black', 'Gray', 'Navy'), 'sizes', JSON_ARRAY('S', 'M', 'L', 'XL', 'XXL')));

-- 插入商品变体数据
INSERT INTO `product_variants` (`id`, `product_id`, `sku`, `name`, `price`, `attributes`) VALUES
('var-iphone15pro-nt-128', 'prod-iphone15pro', 'IPHONE15PRO-NT-128', 'iPhone 15 Pro Natural Titanium 128GB', 7999.00,
 JSON_OBJECT('color', 'Natural Titanium', 'storage', '128GB')),
('var-iphone15pro-nt-256', 'prod-iphone15pro', 'IPHONE15PRO-NT-256', 'iPhone 15 Pro Natural Titanium 256GB', 8999.00,
 JSON_OBJECT('color', 'Natural Titanium', 'storage', '256GB')),
('var-iphone15pro-bt-128', 'prod-iphone15pro', 'IPHONE15PRO-BT-128', 'iPhone 15 Pro Blue Titanium 128GB', 7999.00,
 JSON_OBJECT('color', 'Blue Titanium', 'storage', '128GB')),

('var-macbookpro-sg-8-512', 'prod-macbookpro', 'MACBOOKPRO14-SG-8-512', 'MacBook Pro 14" Space Gray 8GB 512GB', 14999.00,
 JSON_OBJECT('color', 'Space Gray', 'memory', '8GB', 'storage', '512GB')),
('var-macbookpro-sg-16-1tb', 'prod-macbookpro', 'MACBOOKPRO14-SG-16-1TB', 'MacBook Pro 14" Space Gray 16GB 1TB', 17999.00,
 JSON_OBJECT('color', 'Space Gray', 'memory', '16GB', 'storage', '1TB')),

('var-tshirt-white-m', 'prod-tshirt-basic', 'TSHIRT-BASIC-WHITE-M', '基础款T恤 白色 M码', 99.00,
 JSON_OBJECT('color', 'White', 'size', 'M')),
('var-tshirt-black-l', 'prod-tshirt-basic', 'TSHIRT-BASIC-BLACK-L', '基础款T恤 黑色 L码', 99.00,
 JSON_OBJECT('color', 'Black', 'size', 'L'));