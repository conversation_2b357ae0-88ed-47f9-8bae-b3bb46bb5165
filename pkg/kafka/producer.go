package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/segmentio/kafka-go"
)

// Producer Ka<PERSON><PERSON>生产者配置
type Producer struct {
	writer *kafka.Writer
	topic  string
}

// ProducerConfig Kafka生产者配置
type ProducerConfig struct {
	Brokers []string
	Topic   string
}

// NewProducer 创建Kafka生产者
func NewProducer(config ProducerConfig) *Producer {
	writer := &kafka.Writer{
		Addr:         kafka.TCP(config.Brokers...),
		Topic:        config.Topic,
		Balancer:     &kafka.LeastBytes{},
		RequiredAcks: kafka.RequireOne,
		Async:        false, // 同步发送确保可靠性
		Compression:  kafka.Snappy,
		BatchTimeout: 10 * time.Millisecond,
		BatchSize:    10000,
	}

	return &Producer{
		writer: writer,
		topic:  config.Topic,
	}
}

// PublishEvent 发布事件到Kafka
func (p *Producer) PublishEvent(ctx context.Context, key string, event interface{}) error {
	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	message := kafka.Message{
		Key:   []byte(key),
		Value: eventData,
		Time:  time.Now(),
	}

	err = p.writer.WriteMessages(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to write message to kafka: %w", err)
	}

	log.Printf("Event published to Kafka topic %s: key=%s", p.topic, key)
	return nil
}

// Close 关闭Kafka生产者
func (p *Producer) Close() error {
	return p.writer.Close()
}
