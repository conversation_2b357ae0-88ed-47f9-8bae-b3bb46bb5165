# 微服务项目架构设计与文档计划

## 1. 高层架构设计

### 1.1 核心微服务及其职责

*   **用户服务 (User Service)**:
    *   职责：管理用户注册、用户信息查询与修改。
    *   事件发布：`UserRegistered` (用户注册成功), `UserUpdated` (用户信息更新)。
    *   事件订阅：无。
*   **认证授权服务 (Auth Service)**:
    *   职责：管理用户登录、认证（Authentication）、授权（Authorization）、Token 管理（JWT 等）。
    *   事件发布：`UserLoggedIn` (用户登录成功), `UserLoggedOut` (用户登出)。
    *   事件订阅：`UserRegistered` (用户注册成功，用于初始化认证信息)。
*   **库存服务 (Inventory Service)**:
    *   职责：管理商品库存、库存扣减、库存回滚、库存查询。
    *   事件发布：`InventoryReserved` (库存预留成功), `InventoryReleased` (库存释放), `InventoryDeducted` (库存扣减成功)。
    *   事件订阅：`OrderCreated` (订单创建事件，用于预留库存), `OrderCancelled` (订单取消事件，用于释放库存), `PaymentFailed` (支付失败事件，用于释放库存)。
*   **订单服务 (Order Service)**:
    *   职责：管理订单创建、订单查询、订单状态更新、订单取消。
    *   事件发布：`OrderCreated` (订单创建), `OrderPaid` (订单支付成功), `OrderCancelled` (订单取消)。
    *   事件订阅：`InventoryReserved` (库存预留成功，更新订单状态), `PaymentSuccess` (支付成功，更新订单状态), `PaymentFailed` (支付失败，更新订单状态)。
*   **支付服务 (Payment Service)**:
    *   职责：处理支付请求、支付回调、支付状态查询。
    *   事件发布：`PaymentSuccess` (支付成功), `PaymentFailed` (支付失败)。
    *   事件订阅：`OrderCreated` (订单创建，发起支付)。

### 1.2 事件驱动模式应用

*   **核心思想**：服务之间通过发布和订阅事件进行异步通信，降低耦合度，提高系统弹性。
*   **典型流程**：
    1.  用户在前端创建订单。
    2.  前端调用**订单服务**的 API 创建订单。
    3.  **订单服务**创建订单记录，并发布 `OrderCreated` 事件到消息队列。
    4.  **库存服务**订阅 `OrderCreated` 事件，接收到事件后，预留商品库存，并发布 `InventoryReserved` 事件。
    5.  **订单服务**订阅 `InventoryReserved` 事件，接收到事件后，更新订单状态为“待支付”，并通知**支付服务**发起支付。
    6.  **支付服务**处理支付请求，支付成功后发布 `PaymentSuccess` 事件，失败则发布 `PaymentFailed` 事件。
    7.  **订单服务**订阅 `PaymentSuccess` 或 `PaymentFailed` 事件，更新订单状态。
    8.  **库存服务**订阅 `PaymentSuccess` 事件，接收到事件后，扣减实际库存。如果支付失败，则订阅 `PaymentFailed` 事件，释放预留库存。

### 1.3 消息队列选型建议

*   **建议**：Kafka 或 RabbitMQ。
*   **理由**：
    *   **Kafka**：
        *   **高吞吐量和可伸缩性**：适合处理大量事件流，支持水平扩展。
        *   **持久化存储**：事件数据可持久化，支持消费者离线后重新消费。
        *   **分布式特性**：天然支持分布式部署，高可用。
        *   **适用场景**：事件溯源、日志收集、大数据流处理等。
    *   **RabbitMQ**：
        *   **成熟稳定**：广泛应用于企业级应用，社区活跃。
        *   **灵活的路由**：支持多种消息模式（点对点、发布/订阅、路由、主题），满足复杂的消息分发需求。
        *   **可靠性**：支持消息确认、持久化、事务等，保证消息不丢失。
        *   **适用场景**：任务队列、异步通信、解耦服务。
*   **选择建议**：
    *   如果项目初期对消息吞吐量要求极高，且未来可能涉及大数据分析或事件溯源，优先考虑 **Kafka**。
    *   如果更注重消息的可靠性、灵活的路由和易用性，且初期吞吐量要求适中，优先考虑 **RabbitMQ**。
    *   考虑到这是一个电商场景，事件量可能较大，且未来可能扩展到更复杂的业务，**Kafka** 可能是更具前瞻性的选择。

### 1.4 数据库和缓存应用方式

*   **数据库 (MySQL)**:
    *   **每个微服务拥有独立的数据库**：这是微服务架构的核心原则之一，确保服务自治性，避免数据耦合。例如，用户服务有自己的用户数据库，订单服务有自己的订单数据库。
    *   **数据一致性**：通过事件驱动模式实现最终一致性。例如，订单创建后，库存服务通过订阅事件来更新库存，而不是直接访问订单服务的数据库。
    *   **事务管理**：每个服务内部的事务由其自身管理，跨服务的事务通过事件补偿机制（Saga 模式）来保证。
*   **缓存 (Redis)**:
    *   **读缓存**：用于存储热点数据，减轻数据库压力，提高查询性能。例如，用户服务可以将用户常用信息缓存到 Redis。
    *   **分布式锁**：在需要保证分布式环境下资源互斥访问时使用，例如库存扣减时的并发控制。
    *   **消息发布/订阅**：Redis 也可以作为轻量级的消息队列使用，但对于本项目的事件驱动模式，更推荐专业的 MQ。
    *   **会话管理**：存储用户会话信息。

## 2. 技术栈选型建议

### 2.1 Golang 微服务框架

*   **建议**：**Gin + gRPC+SQLC** 组合。
*   **理由**：
    *   **Gin (Web 框架)**：
        *   **高性能**：Gin 是一个高性能的 HTTP Web 框架，适合构建 RESTful API。
        *   **轻量级**：核心功能简洁，易于学习和使用。
        *   **中间件支持**：丰富的中间件生态，方便实现日志、认证、限流等功能。
        *   **适用场景**：对外暴露的 RESTful API 接口，如用户注册、登录、订单查询等。
    *   **gRPC (RPC 框架)**：
        *   **高性能**：基于 HTTP/2 和 Protocol Buffers，提供高效的二进制传输。
        *   **多语言支持**：通过 Protobuf 定义服务接口，支持多种语言的客户端和服务器，便于跨语言服务调用。
        *   **强类型接口**：Protobuf 强制定义服务接口和消息结构，减少运行时错误。
        *   **适用场景**：微服务内部之间的高效通信，如订单服务调用库存服务预留库存。
    *   **组合优势**：
        *   **内外兼顾**：Gin 负责对外提供 RESTful API，gRPC 负责内部服务间的高效通信，各司其职。
        *   **性能优化**：内部通信使用 gRPC 避免了 JSON 序列化和反序列化的开销。
        *   **可扩展性**：两者都易于扩展，方便未来业务增长。
        *   **社区活跃**：Gin 和 gRPC 在 Go 社区都有广泛的应用和活跃的社区支持。
    *   **SQLC (ORM)**：
        *   **类型安全**：生成的代码与数据库表结构强绑定，减少类型错误。
        *   **性能优化**：避免了反射的使用，提高了查询性能。
        *   **代码生成**：根据 SQL 语句自动生成类型安全的 Go 代码，减少样板代码。
        *   **适用场景**：数据库访问层，提供类型安全的查询和操作。
    
*   **其他考虑**：
    *   **Go-Micro / Go-Kit**：这些是更全面的微服务框架，提供了服务发现、负载均衡、熔断等开箱即用的功能。如果项目规模较大，且团队对这些框架有经验，也可以考虑。但对于初期项目，Gin + gRPC 组合更轻量，学习曲线更平缓，且能满足大部分需求。

### 2.2 前端技术栈

*   **确认**：Vue + Element Plus + Vue Router + Pinia。
*   **理由**：
    *   **Vue.js**：渐进式 JavaScript 框架，易学易用，生态系统完善。
    *   **Element Plus**：基于 Vue 3 的桌面端组件库，提供丰富的 UI 组件，加速开发。
    *   **Vue Router**：Vue 官方路由管理器，用于构建单页面应用。
    *   **Pinia**：Vue 官方推荐的状态管理库，轻量级，易于使用和理解。
*   **整体优势**：这套技术栈是 Vue 生态中非常成熟和流行的组合，能够高效地构建美观、功能完善的后台管理系统或电商前端。

## 3. 文档结构建议 (敏捷开发模式)

在敏捷开发模式下，文档应精简、实用，并随着项目的迭代而更新。以下是项目初期建议生成的具体文档清单及简要内容大纲：

1.  **系统概述 (System Overview)**
    *   **目的**：提供项目的高层视图，帮助所有参与者快速理解系统。
    *   **内容大纲**：
        *   项目背景与目标
        *   核心业务流程概述 (例如：用户注册 -> 订单创建 -> 支付 -> 库存扣减)
        *   系统范围与边界
        *   主要用户角色及其交互
        *   高层架构图 (例如：微服务模块图，展示服务间关系)

2.  **服务划分与职责 (Service Division and Responsibilities)**
    *   **目的**：详细定义每个微服务的职责、边界和对外接口。
    *   **内容大纲**：
        *   每个微服务的名称、核心职责
        *   每个服务管理的数据实体 (例如：用户服务管理 User, Address)
        *   每个服务对外暴露的 API 接口 (RESTful API 或 gRPC 接口定义)
        *   服务间依赖关系 (如果存在同步调用)

3.  **技术选型说明 (Technology Stack Selection)**
    *   **目的**：详细说明所选技术栈的理由和使用规范。
    *   **内容大纲**：
        *   后端语言：Golang (版本，为什么选择 Go)
        *   微服务框架：Gin + gRPC (为什么选择，如何使用)
        *   数据库：MySQL (版本，连接池配置，ORM 选型建议)
        *   缓存：Redis (版本，使用场景，连接配置)
        *   消息队列：Kafka/RabbitMQ (为什么选择，主题/队列命名规范，消息结构定义)
        *   前端框架：Vue + Element Plus + Vue Router + Pinia (版本，使用规范)
        *   其他工具/库 (例如：日志库、配置管理、监控工具等)

4.  **数据库设计初步 (Preliminary Database Design)**
    *   **目的**：为每个微服务提供其独立数据库的初步表结构设计。
    *   **内容大纲**：
        *   每个微服务对应的数据库名称
        *   每个数据库中的核心表结构 (表名、字段、数据类型、主键、外键、索引)
        *   表之间的关系 (ER 图)
        *   数据迁移/版本管理工具建议 (例如：Goose, Flyway)

5.  **事件定义与流转 (Event Definition and Flow)**
    *   **目的**：明确系统中所有事件的定义、发布者、订阅者和事件流转路径。
    *   **内容大纲**：
        *   事件列表 (事件名称、描述、包含的数据字段)
        *   每个事件的发布者 (Producer)
        *   每个事件的订阅者 (Consumer)
        *   事件流转图 (使用 Mermaid Sequence Diagram 或 C4 Model 中的 C3 容器图)
        *   事件幂等性处理策略
        *   事件最终一致性保证机制

6.  **API 接口文档 (API Documentation)**
    *   **目的**：详细描述每个微服务对外暴露的 API 接口，供前端或其他服务调用。
    *   **内容大纲**：
        *   接口路径、HTTP 方法 (RESTful) 或 gRPC 服务/方法
        *   请求参数 (Request Body/Query Params)
        *   响应结构 (Response Body)
        *   错误码定义
        *   认证与授权机制
        *   工具建议：Swagger/OpenAPI (RESTful), Protobuf 定义文件 (gRPC)

7.  **部署与运维初步 (Preliminary Deployment and Operations)**
    *   **目的**：初步规划系统的部署方式和运维考虑，确保系统可观测性和稳定性。
    *   **内容大纲**：
        *   部署环境 (开发、测试、生产)
        *   部署方式建议 (Docker, Kubernetes)
        *   **日志管理**：
            *   日志级别定义 (DEBUG, INFO, WARN, ERROR, FATAL)
            *   日志格式规范 (JSON 格式，包含时间戳、服务名、请求 ID、链路 ID 等)
            *   日志收集方案 (例如：Filebeat/Fluentd 收集日志，发送到 Kafka/Elasticsearch)
            *   日志存储与查询 (例如：Elasticsearch + Kibana)
            *   日志审计与安全考虑
        *   **监控与告警**：
            *   指标类型 (业务指标、系统指标、应用指标)
            *   监控工具选型 (例如：Prometheus 收集指标，Grafana 进行可视化)
            *   告警策略 (例如：CPU/内存使用率、错误率、响应时间、消息队列积压等)
            *   告警通知渠道 (例如：邮件、短信、钉钉、Slack)
            *   链路追踪 (例如：Jaeger/Zipkin，用于分布式系统调用链分析)
        *   持续集成/持续部署 (CI/CD) 流程初步设想
        *   服务治理 (服务发现、负载均衡、熔断、限流等初步考虑)

## 架构图 (Mermaid)

```mermaid
graph TD
    subgraph Frontend
        User_Browser[用户浏览器]
    end

    subgraph API_Gateway
        API_GW[API 网关]
    end

    subgraph Microservices
        subgraph User_Service
            US[用户服务]
            US_DB[(用户数据库)]
        end

        subgraph Auth_Service
            AS[认证授权服务]
            AS_DB[(认证授权数据库)]
        end

        subgraph Inventory_Service
            IS[库存服务]
            IS_DB[(库存数据库)]
        end

        subgraph Order_Service
            OS[订单服务]
            OS_DB[(订单数据库)]
        end

        subgraph Payment_Service
            PS[支付服务]
            PS_DB[(支付数据库)]
        end
    end

    subgraph Message_Queue
        MQ[消息队列 (Kafka/RabbitMQ)]
    end

    subgraph Cache
        Redis[Redis 缓存]
    end

    User_Browser --> API_GW
    API_GW --> US
    API_GW --> AS
    API_GW --> OS
    API_GW --> PS

    US --> US_DB
    AS --> AS_DB
    IS --> IS_DB
    OS --> OS_DB
    PS --> PS_DB

    US -- 发布 UserRegistered 事件 --> MQ
    MQ -- 订阅 UserRegistered 事件 --> AS

    OS -- 发布 OrderCreated 事件 --> MQ
    MQ -- 订阅 OrderCreated 事件 --> IS
    IS -- 发布 InventoryReserved 事件 --> MQ
    MQ -- 订阅 InventoryReserved 事件 --> OS
    OS -- 调用支付服务 (gRPC/REST) --> PS
    PS -- 发布 PaymentSuccess/PaymentFailed 事件 --> MQ
    MQ -- 订阅 PaymentSuccess/PaymentFailed 事件 --> OS
    MQ -- 订阅 PaymentSuccess/PaymentFailed 事件 --> IS

    US -- 读写缓存 --> Redis
    AS -- 读写缓存 --> Redis
    IS -- 读写缓存 --> Redis
    OS -- 读写缓存 --> Redis
    PS -- 读写缓存 --> Redis

    style API_GW fill:#f9f,stroke:#333,stroke-width:2px
    style US fill:#bbf,stroke:#333,stroke-width:2px
    style AS fill:#bbf,stroke:#333,stroke-width:2px
    style IS fill:#bbf,stroke:#333,stroke-width:2px
    style OS fill:#bbf,stroke:#333,stroke-width:2px
    style PS fill:#bbf,stroke:#333,stroke-width:2px
    style MQ fill:#ccf,stroke:#333,stroke-width:2px
    style Redis fill:#fcf,stroke:#333,stroke-width:2px
```

## 事件流转示例 (Mermaid Sequence Diagram)

```mermaid
sequenceDiagram
    participant F as 前端
    participant US as 用户服务
    participant AS as 认证授权服务
    participant OS as 订单服务
    participant MQ as 消息队列
    participant IS as 库存服务
    participant PS as 支付服务

    F->>US: 用户注册请求 (API)
    US->>US: 保存用户信息
    US->>MQ: 发布 UserRegistered 事件
    MQ-->>AS: 转发 UserRegistered 事件
    AS->>AS: 初始化用户认证信息

    F->>AS: 用户登录请求 (API)
    AS->>AS: 认证并生成 Token
    AS->>F: 返回 Token

    F->>OS: 创建订单请求 (API, 带 Token)
    OS->>OS: 验证 Token (可能通过调用 AS)
    OS->>OS: 保存订单草稿
    OS->>MQ: 发布 OrderCreated 事件
    MQ-->>IS: 转发 OrderCreated 事件
    IS->>IS: 预留库存
    IS->>MQ: 发布 InventoryReserved 事件
    MQ-->>OS: 转发 InventoryReserved 事件
    OS->>OS: 更新订单状态为“待支付”
    OS->>PS: 发起支付请求 (gRPC/REST)
    PS->>PS: 处理支付逻辑
    PS->>MQ: 发布 PaymentSuccess/PaymentFailed 事件
    MQ-->>OS: 转发 PaymentSuccess/PaymentFailed 事件
    OS->>OS: 更新订单状态 (已支付/支付失败)
    MQ-->>IS: 转发 PaymentSuccess/PaymentFailed 事件
    alt PaymentSuccess
        IS->>IS: 扣减实际库存
    else PaymentFailed
        IS->>IS: 释放预留库存
    end