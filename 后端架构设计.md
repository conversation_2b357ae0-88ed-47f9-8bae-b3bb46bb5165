# 后端架构设计

## 1. 代码框架结构

基于当前项目结构，特别是 `services` 目录下的微服务（如 `user-service`），后端服务的代码组织结构将遵循以下模式：

### 根目录 (`pay-mall`)

*   `go.mod`, `go.work`, `go.work.sum`: Go 模块文件，用于管理整个项目的依赖和多模块工作区。
*   `services/`: 包含所有独立的微服务。每个微服务都是一个独立的 Go 模块。
*   `pkg/`: 存放公共包，例如 Protobuf 定义 (`pkg/proto`)、通用的工具函数、错误处理等。
*   `docs/`: 存放项目文档，如 `后端架构设计.md`、`项目需求清单.md` 等。

### 微服务目录 (`services/<service-name>`)

每个微服务（例如 `services/user-service`）内部将采用以下标准结构：

*   `cmd/`: 包含服务的入口点，通常是 `main.go` 文件，负责服务的初始化、路由注册和启动。
*   `internal/`: 存放服务内部的私有代码，不应被其他服务直接导入。
    *   `handler/`: 处理外部请求（HTTP/gRPC）的逻辑层。负责请求解析、参数校验、调用业务逻辑层，并返回响应。例如 `user_handler.go`。
    *   `service/` (或 `core/`): 业务逻辑层。包含核心业务规则和协调多个 `repository` 操作。
    *   `repository/`: 数据访问层。负责与数据库或其他持久化存储进行交互，提供数据存取接口。例如 `user_repository.go`。
    *   `model/` (可选): 定义服务内部的数据模型或实体。
*   `api/` (可选): 如果服务提供 gRPC 接口，这里可能存放 gRPC 服务的实现。
*   `go.mod`, `go.sum`: 微服务自身的 Go 模块文件，管理该服务的特定依赖。

## 2. 数据库表结构设计

为支付商城项目设计核心的数据库表结构。考虑到微服务架构，每个服务可能拥有自己的数据库，但这里提供一个逻辑上的整体视图。

### 用户服务 (User Service) 数据库

*   **`users` 表** (用户表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 用户唯一ID，UUID。
    *   `username` (VARCHAR(50), UNIQUE, NOT NULL): 用户名。
    *   `email` (VARCHAR(100), UNIQUE, NOT NULL): 邮箱地址。
    *   `password_hash` (VARCHAR(255), NOT NULL): 密码哈希值。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

### 商品服务 (Product Service) 数据库

*   **`products` 表** (商品表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 商品唯一ID，UUID。
    *   `name` (VARCHAR(255), NOT NULL): 商品名称。
    *   `description` (TEXT): 商品描述。
    *   `price` (DECIMAL(10, 2), NOT NULL): 商品价格。
    *   `original_price` (DECIMAL(10, 2)): 商品原价（用于显示折扣）。
    *   `image_url` (VARCHAR(255)): 商品主图片URL。
    *   `image_urls` (JSON): 商品多图片URL数组。
    *   `category_id` (VARCHAR(36), NOT NULL): 商品分类ID (外键，关联 `categories` 表)。
    *   `brand` (VARCHAR(100)): 商品品牌。
    *   `sku` (VARCHAR(100), UNIQUE): 商品SKU编码。
    *   `weight` (DECIMAL(8, 3)): 商品重量（千克）。
    *   `dimensions` (JSON): 商品尺寸信息 (长x宽x高)。
    *   `tags` (JSON): 商品标签数组。
    *   `attributes` (JSON): 商品属性（颜色、尺码等）。
    *   `status` (VARCHAR(20), NOT NULL, DEFAULT 'ACTIVE'): 商品状态 (ACTIVE, INACTIVE, DELETED)。
    *   `sort_order` (INT, DEFAULT 0): 排序权重。
    *   `view_count` (INT, DEFAULT 0): 浏览次数。
    *   `sale_count` (INT, DEFAULT 0): 销售数量。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

*   **`categories` 表** (商品分类表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 分类唯一ID，UUID。
    *   `name` (VARCHAR(100), UNIQUE, NOT NULL): 分类名称。
    *   `description` (TEXT): 分类描述。
    *   `parent_id` (VARCHAR(36)): 父分类ID，支持多级分类。
    *   `level` (INT, NOT NULL, DEFAULT 1): 分类层级。
    *   `path` (VARCHAR(500)): 分类路径，如 "/electronics/phones"。
    *   `icon_url` (VARCHAR(255)): 分类图标URL。
    *   `sort_order` (INT, DEFAULT 0): 排序权重。
    *   `status` (VARCHAR(20), NOT NULL, DEFAULT 'ACTIVE'): 分类状态 (ACTIVE, INACTIVE)。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

*   **`product_variants` 表** (商品变体表，支持多规格商品)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 变体唯一ID，UUID。
    *   `product_id` (VARCHAR(36), NOT NULL): 商品ID (外键，关联 `products` 表)。
    *   `sku` (VARCHAR(100), UNIQUE, NOT NULL): 变体SKU编码。
    *   `name` (VARCHAR(255), NOT NULL): 变体名称，如 "红色-L码"。
    *   `price` (DECIMAL(10, 2), NOT NULL): 变体价格。
    *   `attributes` (JSON, NOT NULL): 变体属性 {"color": "red", "size": "L"}。
    *   `image_url` (VARCHAR(255)): 变体专属图片URL。
    *   `status` (VARCHAR(20), NOT NULL, DEFAULT 'ACTIVE'): 变体状态。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

### 订单服务 (Order Service) 数据库

*   **`orders` 表** (订单表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 订单唯一ID，UUID。
    *   `user_id` (VARCHAR(36), NOT NULL): 用户ID (外键，关联 `users` 表)。
    *   `total_amount` (DECIMAL(10, 2), NOT NULL): 订单总金额。
    *   `status` (VARCHAR(50), NOT NULL): 订单状态 (e.g., PENDING, PAID, SHIPPED, CANCELLED)。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

*   **`order_items` 表** (订单项表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 订单项唯一ID，UUID。
    *   `order_id` (VARCHAR(36), NOT NULL): 订单ID (外键，关联 `orders` 表)。
    *   `product_id` (VARCHAR(36), NOT NULL): 商品ID (外键，关联 `products` 表)。
    *   `quantity` (INT, NOT NULL): 商品数量。
    *   `price` (DECIMAL(10, 2), NOT NULL): 单价 (下单时的价格)。

### 支付服务 (Payment Service) 数据库

*   **`payments` 表** (支付记录表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 支付记录唯一ID，UUID。
    *   `order_id` (VARCHAR(36), UNIQUE, NOT NULL): 订单ID (外键，关联 `orders` 表)。
    *   `user_id` (VARCHAR(36), NOT NULL): 用户ID (外键，关联 `users` 表)。
    *   `amount` (DECIMAL(10, 2), NOT NULL): 支付金额。
    *   `payment_method` (VARCHAR(50), NOT NULL): 支付方式 (e.g., ALIPAY, WECHAT_PAY, CREDIT_CARD)。
    *   `transaction_id` (VARCHAR(255), UNIQUE): 支付平台交易ID。
    *   `status` (VARCHAR(50), NOT NULL): 支付状态 (e.g., PENDING, SUCCESS, FAILED, REFUNDED)。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

### 库存服务 (Inventory Service) 数据库

*   **`inventory` 表** (库存表)
    *   `product_id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 商品ID (外键，关联 `products` 表)。
    *   `stock` (INT, NOT NULL): 当前库存量。
    *   `reserved_stock` (INT, DEFAULT 0): 预留库存量 (例如，订单已创建但未支付)。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

## 3. 事件领域

在支付商城系统中，关键业务事件将通过事件驱动架构进行发布和订阅，以实现服务间的解耦和异步通信。

### 事件流图

```mermaid
graph TD
    A[用户服务] -- 用户注册 --> B(UserRegistered Event)
    B -- 订阅 --> C[订单服务]
    B -- 订阅 --> D[其他服务]

    P[商品服务] -- 商品创建 --> P1(ProductCreated Event)
    P1 -- 订阅 --> G[库存服务]
    P -- 商品更新 --> P2(ProductUpdated Event)
    P2 -- 订阅 --> G[库存服务]
    P2 -- 订阅 --> E[订单服务]
    P -- 商品删除 --> P3(ProductDeleted Event)
    P3 -- 订阅 --> G[库存服务]
    P3 -- 订阅 --> E[订单服务]

    E[订单服务] -- 订单创建 --> F(OrderCreated Event)
    F -- 订阅 --> G[库存服务]
    F -- 订阅 --> H[支付服务]

    I[支付服务] -- 支付成功 --> J(PaymentSucceeded Event)
    J -- 订阅 --> E[订单服务]
    J -- 订阅 --> G[库存服务]
    J -- 订阅 --> P[商品服务]
    J -- 订阅 --> K[通知服务]

    L[订单服务] -- 订单取消 --> M(OrderCancelled Event)
    M -- 订阅 --> G[库存服务]
    M -- 订阅 --> K[通知服务]

    G[库存服务] -- 库存更新 --> N(InventoryUpdated Event)
    N -- 订阅 --> E[订单服务]
    N -- 订阅 --> P[商品服务]
    N -- 订阅 --> K[通知服务]
```

### 关键业务事件列表

1.  **事件名称**: `UserRegistered` (用户注册)
    *   **发布者**: 用户服务 (User Service)
    *   **订阅者**: 订单服务 (Order Service) (例如，为新用户创建默认地址或优惠券)、其他需要用户信息的服务。
    *   **事件内容**:
        ```json
        {
          "user_id": "string",
          "username": "string",
          "email": "string",
          "timestamp": "datetime"
        }
        ```

2.  **事件名称**: `ProductCreated` (商品创建)
    *   **发布者**: 商品服务 (Product Service)
    *   **订阅者**: 库存服务 (Inventory Service) (初始化商品库存记录)。
    *   **事件内容**:
        ```json
        {
          "product_id": "string",
          "name": "string",
          "category_id": "string",
          "price": "decimal",
          "sku": "string",
          "variants": [
            {
              "variant_id": "string",
              "sku": "string",
              "attributes": "object"
            }
          ],
          "timestamp": "datetime"
        }
        ```

3.  **事件名称**: `ProductUpdated` (商品更新)
    *   **发布者**: 商品服务 (Product Service)
    *   **订阅者**: 库存服务 (Inventory Service) (更新商品信息)、订单服务 (Order Service) (处理价格变更影响)。
    *   **事件内容**:
        ```json
        {
          "product_id": "string",
          "name": "string",
          "old_price": "decimal",
          "new_price": "decimal",
          "status": "string",
          "changes": "object",
          "timestamp": "datetime"
        }
        ```

4.  **事件名称**: `ProductDeleted` (商品删除)
    *   **发布者**: 商品服务 (Product Service)
    *   **订阅者**: 库存服务 (Inventory Service) (清理库存记录)、订单服务 (Order Service) (处理相关订单)。
    *   **事件内容**:
        ```json
        {
          "product_id": "string",
          "name": "string",
          "reason": "string",
          "timestamp": "datetime"
        }
        ```

5.  **事件名称**: `OrderCreated` (订单创建)
    *   **发布者**: 订单服务 (Order Service)
    *   **订阅者**: 库存服务 (Inventory Service) (预留库存)、支付服务 (Payment Service) (准备支付)。
    *   **事件内容**:
        ```json
        {
          "order_id": "string",
          "user_id": "string",
          "total_amount": "decimal",
          "items": [
            {
              "product_id": "string",
              "quantity": "integer",
              "price": "decimal"
            }
          ],
          "timestamp": "datetime"
        }
        ```

6.  **事件名称**: `PaymentSucceeded` (支付成功)
    *   **发布者**: 支付服务 (Payment Service)
    *   **订阅者**: 订单服务 (Order Service) (更新订单状态为“已支付”)、库存服务 (Inventory Service) (扣减实际库存)、商品服务 (Product Service) (更新销售统计)、通知服务 (Notification Service) (发送支付成功通知)。
    *   **事件内容**:
        ```json
        {
          "payment_id": "string",
          "order_id": "string",
          "user_id": "string",
          "amount": "decimal",
          "transaction_id": "string",
          "items": [
            {
              "product_id": "string",
              "quantity": "integer",
              "price": "decimal"
            }
          ],
          "timestamp": "datetime"
        }
        ```

7.  **事件名称**: `InventoryUpdated` (库存更新)
    *   **发布者**: 库存服务 (Inventory Service)
    *   **订阅者**: 订单服务 (Order Service) (例如，处理库存不足导致的订单失败)、商品服务 (Product Service) (更新商品库存显示)、通知服务 (Notification Service) (例如，库存预警)。
    *   **事件内容**:
        ```json
        {
          "product_id": "string",
          "new_stock": "integer",
          "change_amount": "integer",
          "reason": "string", // e.g., "order_paid", "order_cancelled", "manual_adjustment"
          "timestamp": "datetime"
        }
        ```

8.  **事件名称**: `OrderCancelled` (订单取消)
    *   **发布者**: 订单服务 (Order Service)
    *   **订阅者**: 库存服务 (Inventory Service) (释放预留库存)、通知服务 (Notification Service) (发送订单取消通知)。
    *   **事件内容**:
        ```json
        {
          "order_id": "string",
          "user_id": "string",
          "reason": "string",
          "timestamp": "datetime"
        }
        ```

## 4. 核心业务状态流转

为了更清晰地管理核心业务流程中的实体状态，我们将为商品、订单和支付定义详细的状态流转。这些状态将作为数据库中 `status` 字段的值，并通过事件驱动机制进行状态转换。

### 4.1 商品状态流转

商品状态反映了商品从创建到下架的整个生命周期。

#### 4.1.1 商品状态定义

*   **`DRAFT` (草稿)**: 商品已创建但尚未发布，仅管理员可见。
*   **`ACTIVE` (上架)**: 商品已发布，用户可正常浏览和购买。
*   **`INACTIVE` (下架)**: 商品暂时下架，用户不可购买但可查看。
*   **`OUT_OF_STOCK` (缺货)**: 商品库存为0，用户不可购买。
*   **`DISCONTINUED` (停产)**: 商品停产，不再销售。
*   **`DELETED` (已删除)**: 商品已删除，仅保留历史记录。

#### 4.1.2 商品状态流转图

```mermaid
stateDiagram-v2
    [*] --> DRAFT: 创建商品
    DRAFT --> ACTIVE: 发布商品
    ACTIVE --> INACTIVE: 管理员下架
    INACTIVE --> ACTIVE: 管理员上架
    ACTIVE --> OUT_OF_STOCK: 库存为0 (InventoryUpdated Event)
    OUT_OF_STOCK --> ACTIVE: 补充库存 (InventoryUpdated Event)
    ACTIVE --> DISCONTINUED: 停产
    INACTIVE --> DISCONTINUED: 停产
    DISCONTINUED --> DELETED: 删除商品
    DRAFT --> DELETED: 删除草稿
    DELETED --> [*]
```

### 4.2 订单状态流转

订单状态反映了订单从创建到完成或取消的整个生命周期。

#### 4.2.1 订单状态定义

*   **`PENDING` (待处理)**: 订单已创建，但尚未进行库存预留或支付。
*   **`RESERVED` (已预留库存)**: 订单商品库存已成功预留，等待支付。
*   **`PAID` (已支付)**: 订单已成功支付。
*   **`SHIPPED` (已发货)**: 订单商品已发货。
*   **`DELIVERED` (已送达)**: 订单商品已送达用户。
*   **`CANCELLED` (已取消)**: 订单在支付前或支付后被取消。
*   **`REFUNDED` (已退款)**: 订单已完成退款。

#### 4.2.2 订单状态流转图

```mermaid
stateDiagram-v2
    [*] --> PENDING: 订单创建
    PENDING --> RESERVED: 库存预留成功 (InventoryReserved Event)
    RESERVED --> PAID: 支付成功 (PaymentSucceeded Event)
    PAID --> SHIPPED: 商品发货
    SHIPPED --> DELIVERED: 商品送达
    PENDING --> CANCELLED: 用户取消 / 超时未支付
    RESERVED --> CANCELLED: 用户取消 / 支付失败 (PaymentFailed Event)
    PAID --> REFUNDED: 用户退款 / 异常退款
    CANCELLED --> [*]
    DELIVERED --> [*]
    REFUNDED --> [*]
```

### 4.3 支付状态流转

支付状态反映了支付请求从发起、处理到完成或失败的生命周期。

#### 4.3.1 支付状态定义

*   **`PENDING` (待支付)**: 支付请求已创建，等待用户完成支付。
*   **`SUCCESS` (支付成功)**: 支付已成功完成。
*   **`FAILED` (支付失败)**: 支付尝试失败。
*   **`REFUNDING` (退款中)**: 正在处理退款请求。
*   **`REFUNDED` (已退款)**: 退款已成功完成。

#### 4.3.2 支付状态流转图

```mermaid
stateDiagram-v2
    [*] --> PENDING: 发起支付请求
    PENDING --> SUCCESS: 支付成功回调
    PENDING --> FAILED: 支付失败回调 / 超时
    SUCCESS --> REFUNDING: 发起退款请求
    REFUNDING --> REFUNDED: 退款成功回调
    REFUNDING --> SUCCESS: 退款失败 (回滚到成功状态)
    FAILED --> [*]
    REFUNDED --> [*]
```

## 5. 商品服务详细设计

### 5.1 商品服务架构

商品服务采用分层架构设计，包含以下层次：

*   **Handler层**: 处理HTTP请求，参数验证，响应格式化
*   **Service层**: 业务逻辑处理，事件发布，缓存管理
*   **Repository层**: 数据访问，数据库操作
*   **Model层**: 数据模型定义

### 5.2 商品服务API设计

#### 5.2.1 商品管理API

**创建商品**
```
POST /api/v1/products
Content-Type: application/json

{
  "name": "iPhone 15 Pro",
  "description": "最新款iPhone",
  "price": 7999.00,
  "original_price": 8999.00,
  "category_id": "electronics-001",
  "brand": "Apple",
  "sku": "IPHONE15PRO-001",
  "weight": 0.187,
  "dimensions": {"length": 146.6, "width": 70.6, "height": 8.25},
  "tags": ["smartphone", "5G", "premium"],
  "attributes": {
    "color": ["Natural Titanium", "Blue Titanium", "White Titanium", "Black Titanium"],
    "storage": ["128GB", "256GB", "512GB", "1TB"]
  },
  "image_urls": ["url1", "url2", "url3"],
  "variants": [
    {
      "sku": "IPHONE15PRO-NT-128",
      "name": "Natural Titanium 128GB",
      "price": 7999.00,
      "attributes": {"color": "Natural Titanium", "storage": "128GB"}
    }
  ]
}
```

**获取商品列表**
```
GET /api/v1/products?page=1&page_size=20&category_id=electronics-001&status=ACTIVE&sort=created_at&order=desc
```

**获取商品详情**
```
GET /api/v1/products/{product_id}
```

**更新商品**
```
PUT /api/v1/products/{product_id}
```

**删除商品**
```
DELETE /api/v1/products/{product_id}
```

#### 5.2.2 商品分类API

**创建分类**
```
POST /api/v1/categories
{
  "name": "智能手机",
  "description": "各品牌智能手机",
  "parent_id": "electronics-001",
  "icon_url": "https://example.com/phone-icon.png"
}
```

**获取分类树**
```
GET /api/v1/categories/tree
```

**获取分类列表**
```
GET /api/v1/categories?level=1&status=ACTIVE
```

#### 5.2.3 商品搜索API

**搜索商品**
```
GET /api/v1/products/search?q=iPhone&category_id=electronics-001&min_price=1000&max_price=10000&brand=Apple&sort=price&order=asc
```

**获取热门商品**
```
GET /api/v1/products/popular?limit=10
```

**获取推荐商品**
```
GET /api/v1/products/recommendations?user_id=user-001&limit=10
```

### 5.3 商品服务事件集成

商品服务将在以下场景发布事件：

1. **商品创建**: 发布 `ProductCreated` 事件，通知库存服务初始化库存记录
2. **商品更新**: 发布 `ProductUpdated` 事件，通知相关服务更新商品信息
3. **商品删除**: 发布 `ProductDeleted` 事件，通知相关服务清理数据
4. **商品状态变更**: 发布 `ProductStatusChanged` 事件，通知库存服务和订单服务

### 5.4 商品服务缓存策略

*   **商品详情缓存**: 使用Redis缓存热门商品详情，TTL为1小时
*   **分类树缓存**: 缓存完整的分类树结构，TTL为24小时
*   **搜索结果缓存**: 缓存热门搜索关键词的结果，TTL为30分钟
*   **商品列表缓存**: 缓存分类商品列表的第一页，TTL为15分钟

### 5.5 商品服务性能优化

*   **数据库索引**: 在 `category_id`, `status`, `created_at`, `price` 等字段上建立索引
*   **分页查询**: 使用游标分页提高大数据量查询性能
*   **图片CDN**: 商品图片使用CDN加速访问
*   **搜索引擎**: 集成Elasticsearch提供高性能全文搜索